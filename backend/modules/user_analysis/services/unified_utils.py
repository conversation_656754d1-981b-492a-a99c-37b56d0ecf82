"""
统一的用户分析工具函数
合并user_api.py和user_behavior_api.py中重复的工具函数
"""

import logging
from typing import Dict, List, Optional, Any, TYPE_CHECKING
from datetime import datetime, timedelta
from functools import wraps
from flask import jsonify
import json

# 类型检查时导入，避免循环导入
if TYPE_CHECKING:
    from modules.user_analysis.models.user_behavior_models import UserBehaviorProfile

logger = logging.getLogger(__name__)

def safe_float(value, default=0.0):
    """安全转换为浮点数"""
    if value is None:
        return default
    try:
        return float(value)
    except (ValueError, TypeError):
        return default

def safe_int(value, default=0):
    """安全转换为整数"""
    if value is None:
        return default
    try:
        return int(float(value))
    except (ValueError, TypeError):
        return default

def unified_error_handler(func):
    """统一的错误处理装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"{func.__name__} 失败: {str(e)}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return jsonify({
                'status': 'error', 
                'message': str(e),
                'function': func.__name__
            }), 500
    return wrapper

class UnifiedTimeUtils:
    """统一的时间处理工具类"""
    
    @staticmethod
    def extract_time_from_range(time_range: str, position: str = 'start') -> Optional[str]:
        """
        从时间范围字符串中提取开始或结束时间
        
        Args:
            time_range: 时间范围字符串，如 "2023-01-01 to 2023-01-31"
            position: 'start' 或 'end'
        
        Returns:
            提取的时间字符串
        """
        try:
            if not time_range or ' to ' not in time_range:
                return None
            
            parts = time_range.split(' to ')
            if len(parts) != 2:
                return None
            
            if position == 'start':
                return parts[0].strip()
            elif position == 'end':
                return parts[1].strip()
            else:
                return None
                
        except Exception as e:
            logger.warning(f"提取时间失败: {str(e)}")
            return None
    
    @staticmethod
    def format_datetime(dt_str: str) -> str:
        """格式化日期时间字符串"""
        try:
            if not dt_str:
                return ''
            
            # 尝试解析不同格式的日期时间
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d %H:%M:%S.%f',
                '%Y-%m-%dT%H:%M:%S',
                '%Y-%m-%dT%H:%M:%S.%f',
                '%Y-%m-%d'
            ]
            
            for fmt in formats:
                try:
                    dt = datetime.strptime(dt_str, fmt)
                    return dt.strftime('%Y-%m-%d %H:%M:%S')
                except ValueError:
                    continue
            
            # 如果都不匹配，返回原字符串
            return dt_str
            
        except Exception as e:
            logger.warning(f"格式化日期时间失败: {str(e)}")
            return dt_str
    
    @staticmethod
    def format_time_gap(seconds: float) -> str:
        """格式化时间间隔"""
        try:
            if seconds < 60:
                return f"{seconds:.1f}秒"
            elif seconds < 3600:
                minutes = seconds / 60
                return f"{minutes:.1f}分钟"
            elif seconds < 86400:
                hours = seconds / 3600
                return f"{hours:.1f}小时"
            else:
                days = seconds / 86400
                return f"{days:.1f}天"
        except Exception as e:
            logger.warning(f"格式化时间间隔失败: {str(e)}")
            return f"{seconds}秒"

class UnifiedDataFormatter:
    """统一的数据格式化工具类"""
    
    @staticmethod
    def format_amount(amount: float, precision: int = 2) -> str:
        """格式化金额"""
        try:
            if amount is None:
                return "0.00"
            
            if amount >= 1000000:
                return f"{amount/1000000:.{precision}f}M"
            elif amount >= 1000:
                return f"{amount/1000:.{precision}f}K"
            else:
                return f"{amount:.{precision}f}"
                
        except Exception as e:
            logger.warning(f"格式化金额失败: {str(e)}")
            return "0.00"
    
    @staticmethod
    def format_percentage(value: float, precision: int = 2) -> str:
        """格式化百分比"""
        try:
            if value is None:
                return "0.00%"
            return f"{value * 100:.{precision}f}%"
        except Exception as e:
            logger.warning(f"格式化百分比失败: {str(e)}")
            return "0.00%"
    
    @staticmethod
    def safe_divide(a: float, b: float, default: float = 0) -> float:
        """安全除法"""
        try:
            if b == 0:
                return default
            return a / b
        except Exception:
            return default
    
    @staticmethod
    def safe_percentage(a: float, b: float, default: float = 0) -> float:
        """安全计算百分比"""
        try:
            if b == 0:
                return default
            return (a / b) * 100
        except Exception:
            return default
    
    @staticmethod
    def get_side_text(side: str) -> str:
        """获取交易方向文本"""
        side_map = {
            'buy': '买入',
            'sell': '卖出',
            'long': '做多',
            'short': '做空',
            '1': '买入',
            '0': '卖出',
            'BUY': '买入',
            'SELL': '卖出'
        }
        return side_map.get(str(side).lower(), side)

class UnifiedResponseBuilder:
    """统一的API响应构建器"""
    
    @staticmethod
    def build_success_response(data: Any, message: str = "操作成功") -> Dict:
        """构建成功响应"""
        return {
            'status': 'success',
            'message': message,
            'data': data,
            'timestamp': datetime.now().isoformat()
        }
    
    @staticmethod
    def build_error_response(error: str, details: Dict = None, status_code: int = 500) -> Dict:
        """构建错误响应"""
        response = {
            'status': 'error',
            'message': error,
            'timestamp': datetime.now().isoformat()
        }
        
        if details:
            response['details'] = details
            
        return response
    
    @staticmethod
    def build_not_found_response(resource: str, identifier: str) -> Dict:
        """构建未找到资源的响应"""
        return {
            'status': 'not_found',
            'message': f'未找到{resource}: {identifier}',
            'timestamp': datetime.now().isoformat()
        }
    
    @staticmethod
    def build_batch_response(results: List[Dict], total_count: int) -> Dict:
        """构建批量操作响应"""
        successful = sum(1 for r in results if r.get('status') == 'success')
        failed = total_count - successful
        
        return {
            'status': 'success',
            'data': results,
            'total_count': total_count,
            'summary': {
                'successful': successful,
                'failed': failed,
                'success_rate': UnifiedDataFormatter.safe_percentage(successful, total_count)
            },
            'timestamp': datetime.now().isoformat()
        }

class UnifiedValidators:
    """统一的数据验证工具类"""
    
    @staticmethod
    def validate_user_id(user_id: str) -> bool:
        """验证用户ID格式"""
        if not user_id or not isinstance(user_id, str):
            return False
        
        # 修复：用户ID格式验证 - 要求最少8位长度
        if len(user_id) < 8 or len(user_id) > 50:
            return False
            
        return True
    
    @staticmethod
    def validate_task_id(task_id: str) -> bool:
        """验证任务ID格式"""
        if not task_id or not isinstance(task_id, str):
            return False
        
        # 简单的任务ID格式验证
        if len(task_id) < 1 or len(task_id) > 100:
            return False
            
        return True
    
    @staticmethod
    def validate_date_range(start_date: str, end_date: str) -> bool:
        """验证日期范围"""
        try:
            start = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
            end = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
            return start <= end
        except Exception:
            return False

class UnifiedDescriptionGenerator:
    """统一的描述生成器"""
    
    @staticmethod
    def generate_description(value: float, thresholds: List[float], descriptions: List[str]) -> str:
        """
        根据阈值生成描述
        
        Args:
            value: 要评估的值
            thresholds: 阈值列表（升序）
            descriptions: 对应的描述列表
        
        Returns:
            匹配的描述
        """
        try:
            if len(thresholds) != len(descriptions) - 1:
                return descriptions[0] if descriptions else ""
            
            for i, threshold in enumerate(thresholds):
                if value <= threshold:
                    return descriptions[i]
            
            return descriptions[-1]
            
        except Exception as e:
            logger.warning(f"生成描述失败: {str(e)}")
            return descriptions[0] if descriptions else ""
    
    @staticmethod
    def get_risk_level_description(total_risks: int, max_score: float) -> str:
        """获取风险等级描述"""
        try:
            if total_risks == 0:
                return "无风险"
            elif total_risks <= 5 and max_score < 0.3:
                return "低风险"
            elif total_risks <= 10 and max_score < 0.6:
                return "中等风险"
            elif total_risks <= 20 and max_score < 0.8:
                return "较高风险"
            else:
                return "高风险"
        except Exception:
            return "未知风险"
    
    @staticmethod
    def get_volume_description(total_volume: float) -> str:
        """获取交易量描述"""
        try:
            if total_volume < 10000:
                return "小额交易"
            elif total_volume < 100000:
                return "中等交易"
            elif total_volume < 1000000:
                return "大额交易"
            else:
                return "超大额交易"
        except Exception:
            return "未知交易量"
    
    @staticmethod
    def get_fund_scale_range(category: str) -> str:
        """获取资金规模范围描述"""
        scale_map = {
            'small': '< 1万',
            'medium': '1万 - 10万',
            'large': '10万 - 100万',
            'xlarge': '> 100万',
            'unknown': '未知'
        }
        return scale_map.get(category, '未知')

class UnifiedCacheManager:
    """统一的缓存管理器（简单实现）"""
    
    def __init__(self):
        self._cache = {}
        self._cache_ttl = {}
    
    def get_cached_data(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        try:
            if key not in self._cache:
                return None
            
            # 检查TTL
            if key in self._cache_ttl:
                if datetime.now() > self._cache_ttl[key]:
                    del self._cache[key]
                    del self._cache_ttl[key]
                    return None
            
            return self._cache[key]
            
        except Exception as e:
            logger.warning(f"获取缓存数据失败: {str(e)}")
            return None
    
    def set_cached_data(self, key: str, data: Any, ttl: int = 300):
        """设置缓存数据"""
        try:
            self._cache[key] = data
            if ttl > 0:
                self._cache_ttl[key] = datetime.now() + timedelta(seconds=ttl)
        except Exception as e:
            logger.warning(f"设置缓存数据失败: {str(e)}")
    
    def invalidate_cache(self, pattern: str = None):
        """清除缓存"""
        try:
            if pattern:
                keys_to_remove = [k for k in self._cache.keys() if pattern in k]
                for key in keys_to_remove:
                    del self._cache[key]
                    if key in self._cache_ttl:
                        del self._cache_ttl[key]
            else:
                self._cache.clear()
                self._cache_ttl.clear()
        except Exception as e:
            logger.warning(f"清除缓存失败: {str(e)}")

# 全局实例
unified_cache_manager = UnifiedCacheManager()

def convert_datetime_to_string(obj: Any) -> Any:
    """递归转换对象中的datetime为字符串，并处理 Infinity 和 NaN 值"""
    import math

    try:
        # 🚀 新增：处理 Infinity 和 NaN 值
        if isinstance(obj, (int, float)):
            if math.isinf(obj):
                return 999.99 if obj > 0 else -999.99  # 用大数值替代无穷大
            elif math.isnan(obj):
                return None  # NaN 转为 null

        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, dict):
            return {k: convert_datetime_to_string(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_datetime_to_string(item) for item in obj]
        else:
            return obj
    except Exception as e:
        logger.warning(f"转换数据失败: {str(e)}")
        return obj

class UnifiedUtils:
    """统一工具类 - 提供通用功能"""
    
    def __init__(self, config: Dict = None):
        """初始化工具类"""
        self.config = config or {}
        self.cache = {}
        self.cache_expiry = {}
        self.logger = logging.getLogger(__name__)
        logger.info("统一工具类初始化完成")
    
    def map_to_frontend_format(self, user_profile: 'UserBehaviorProfile', task_id: str = None) -> Dict:
        """
        🚀 将后端用户行为分析结果映射为前端需要的数据格式
        这是方案2的核心功能 - 完善数据映射
        
        Args:
            user_profile: 用户行为分析结果
            task_id: 任务ID
            
        Returns:
            Dict: 前端格式的数据
        """
        logger.info(f"开始将用户 {user_profile.user_id} 的数据映射为前端格式")
        
        # 生成当前时间
        current_time = datetime.now()
        
        # 1. 搜索区域数据 (search_area)
        search_area = {
            'member_id': user_profile.user_id,
            'digital_id': f"D{user_profile.user_id}",  # 生成数字ID
            'task_id': task_id or f"TASK_{user_profile.user_id}_{int(current_time.timestamp())}",
            'contract_tasks': [],  # TODO: 需要查询任务表
            'agent_tasks': [],     # TODO: 需要查询任务表
            'search_method': '系统分析',
            'search_time': current_time.isoformat(),
            'data_source': '合约分析'
        }
        
        # 2. 用户档案数据 (user_profile)
        user_profile_data = {
            'member_id': user_profile.user_id,
            'digital_id': f"D{user_profile.user_id}",
            'user_type': '普通用户',
            'bd_name': '',  # TODO: 需要查询BD关系表
            'total_risk_events': user_profile.abnormal_analysis.risk_events_count if user_profile.abnormal_analysis else 0,
            'last_activity': user_profile.analysis_period_end.isoformat(),
            'search_method': '系统分析',
            'analysis_status': '完成'
        }
        
        # 3. 专业度仪表盘 (professional_dashboard)
        professional_dashboard = {
            'total_score': user_profile.professional_scores.total_score,
            'profitability_score': user_profile.professional_scores.profitability_score,
            'risk_control_score': user_profile.professional_scores.risk_control_score,
            'trading_behavior_score': user_profile.professional_scores.trading_behavior_score,
            'market_understanding_score': user_profile.professional_scores.market_understanding_score,
            'trader_type': user_profile.professional_scores.trader_type,
            'fund_scale_category': user_profile.fund_scale_category,
            'data_quality_score': user_profile.data_quality_score
        }
        
        # 4. 盈亏指标 (pnl_metrics)
        total_pnl = user_profile.basic_metrics.total_profit - user_profile.basic_metrics.total_loss
        pnl_metrics = {
            'win_rate': user_profile.derived_metrics.win_rate,
            'total_pnl': total_pnl,
            'total_profit': user_profile.basic_metrics.total_profit,
            'total_loss': user_profile.basic_metrics.total_loss,
            'profit_loss_ratio': user_profile.derived_metrics.profit_loss_ratio,
            'profit_factor': user_profile.derived_metrics.profit_factor,
            'profitable_trades': user_profile.basic_metrics.profitable_count,
            'loss_trades': user_profile.basic_metrics.loss_count,
            'avg_profit_per_trade': (user_profile.basic_metrics.total_profit / user_profile.basic_metrics.profitable_count 
                                   if user_profile.basic_metrics.profitable_count > 0 else 0),
            'avg_loss_per_trade': (user_profile.basic_metrics.total_loss / user_profile.basic_metrics.loss_count 
                                 if user_profile.basic_metrics.loss_count > 0 else 0),
            # TODO: 需要从positions计算max_profit和max_loss
            'max_profit': 0.0,  
            'max_loss': 0.0
        }
        
        # 5. 币种胜率分析 (coin_win_rate_analysis)
        coin_win_rate_analysis = {}
        if user_profile.coin_win_rate_analysis and user_profile.coin_win_rate_analysis.coin_analysis:
            coin_analysis = user_profile.coin_win_rate_analysis.coin_analysis
            coin_win_rate_analysis = {
                'btc_win_rate': coin_analysis.get('BTC', {}).win_rate if 'BTC' in coin_analysis else 0.0,
                'eth_win_rate': coin_analysis.get('ETH', {}).win_rate if 'ETH' in coin_analysis else 0.0,
                'other_coins_win_rate': {k: v.win_rate for k, v in coin_analysis.items() if k not in ['BTC', 'ETH']},
                'best_performing_coin': self._get_best_performing_coin(coin_analysis),
                'worst_performing_coin': self._get_worst_performing_coin(coin_analysis),
                'total_coins_traded': len(coin_analysis)
            }
        
        # 6. 🚀 币种偏好 (coin_preference) - 来自扩展指标
        coin_preference = {}
        if user_profile.basic_metrics.coin_preference_distribution:
            coin_pref_dist = user_profile.basic_metrics.coin_preference_distribution
            coin_preference = {
                'mainstream_ratio': coin_pref_dist.get('mainstream', 0.0),
                'altcoin_ratio': coin_pref_dist.get('altcoin', 0.0),
                'defi_ratio': coin_pref_dist.get('defi', 0.0),
                'others_ratio': coin_pref_dist.get('others', 0.0),
                'preference_description': self._generate_coin_preference_description(coin_pref_dist)
            }
        
        # 7. 🚀 时间偏好 (time_preference) - 来自扩展指标
        time_preference = {}
        if user_profile.basic_metrics.time_distribution:
            time_dist = user_profile.basic_metrics.time_distribution
            time_preference = {
                'active_hours': time_dist,
                'peak_trading_time': self._get_peak_trading_time(time_dist)
            }
        
        # 8. 杠杆指标 (leverage_indicators)
        leverage_indicators = {
            'avg_leverage': user_profile.derived_metrics.avg_leverage,
            'max_leverage': user_profile.derived_metrics.max_leverage,
            'leverage_stability': user_profile.derived_metrics.leverage_stability,
            'leverage_distribution': user_profile.basic_metrics.leverage_distribution or {}
        }
        
        # 9. 🚀 订单类型指标 (order_type_indicators) - 来自扩展指标
        order_type_indicators = {
            'market_order_ratio': user_profile.derived_metrics.market_order_ratio,
            'limit_order_ratio': 1.0 - user_profile.derived_metrics.market_order_ratio
        }
        if user_profile.basic_metrics.order_type_distribution:
            order_type_dist = user_profile.basic_metrics.order_type_distribution
            order_type_indicators.update({
                'market_open_ratio': order_type_dist.get('market_open_ratio', 0.0),
                'limit_open_ratio': order_type_dist.get('limit_open_ratio', 0.0),
                'market_close_ratio': order_type_dist.get('market_close_ratio', 0.0),
                'limit_close_ratio': order_type_dist.get('limit_close_ratio', 0.0)
            })
        
        # 10. 🚀 仓位规模指标 (position_size_indicators) - 来自扩展指标
        position_size_indicators = {
            'avg_position_size': user_profile.basic_metrics.avg_trade_size,
            'max_position_size': user_profile.basic_metrics.max_trade_amount,
            'min_position_size': user_profile.basic_metrics.min_trade_amount,
            'position_size_consistency': user_profile.derived_metrics.position_size_consistency,
            'size_distribution': user_profile.basic_metrics.position_size_distribution or {}
        }
        
        # 11. 🚀 衍生分析 (derived_analysis) - 前端期望的4个维度结构
        # 🔧 修复：添加空值检查和默认值，防止前端 .toFixed() 错误
        derived_analysis = {
            'profitability': {
                'win_rate': getattr(user_profile.derived_metrics, 'win_rate', 0.0) or 0.0,
                'profit_loss_ratio': getattr(user_profile.derived_metrics, 'profit_loss_ratio', 0.0) or 0.0,
                'profit_factor': getattr(user_profile.derived_metrics, 'profit_factor', 0.0) or 0.0,
                'consistency': getattr(user_profile.derived_metrics, 'profit_consistency', 0.0) or 0.0,
                'profit_consistency': getattr(user_profile.derived_metrics, 'profit_consistency', 0.0) or 0.0,  # 兼容性字段
                'total_score': getattr(user_profile.professional_scores, 'profitability_score', 0.0) or 0.0,
                # 添加评分字段
                'win_rate_score': (getattr(user_profile.professional_scores, 'profitability_score', 0.0) or 0.0) * 0.25,
                'profit_loss_ratio_score': (getattr(user_profile.professional_scores, 'profitability_score', 0.0) or 0.0) * 0.25,
                'profit_factor_score': (getattr(user_profile.professional_scores, 'profitability_score', 0.0) or 0.0) * 0.25,
                'consistency_score': (getattr(user_profile.professional_scores, 'profitability_score', 0.0) or 0.0) * 0.25
            },
            'risk_control': {
                'avg_leverage': getattr(user_profile.derived_metrics, 'avg_leverage', 0.0) or 0.0,
                'max_leverage': getattr(user_profile.derived_metrics, 'max_leverage', 0.0) or 0.0,
                'leverage_stability': getattr(user_profile.derived_metrics, 'leverage_stability', 0.0) or 0.0,
                'max_single_loss': getattr(user_profile.derived_metrics, 'max_single_loss', 0.0) or 0.0,
                'max_single_loss_ratio': getattr(user_profile.derived_metrics, 'max_single_loss_ratio', 0.0) or 0.0,
                'total_score': getattr(user_profile.professional_scores, 'risk_control_score', 0.0) or 0.0,
                # 添加评分字段
                'avg_leverage_score': (getattr(user_profile.professional_scores, 'risk_control_score', 0.0) or 0.0) * 0.25,
                'max_leverage_score': (getattr(user_profile.professional_scores, 'risk_control_score', 0.0) or 0.0) * 0.25,
                'leverage_stability_score': (getattr(user_profile.professional_scores, 'risk_control_score', 0.0) or 0.0) * 0.25,
                'max_single_loss_score': (getattr(user_profile.professional_scores, 'risk_control_score', 0.0) or 0.0) * 0.25
            },
            'trading_behavior': {
                'trading_frequency': getattr(user_profile.derived_metrics, 'trading_frequency', 0.0) or 0.0,
                'market_order_ratio': getattr(user_profile.derived_metrics, 'market_order_ratio', 0.0) or 0.0,
                'duration_ratio': getattr(user_profile.derived_metrics, 'profit_loss_duration_ratio', 0.0) or 0.0,
                'position_consistency': getattr(user_profile.derived_metrics, 'position_size_consistency', 0.0) or 0.0,
                'total_score': getattr(user_profile.professional_scores, 'trading_behavior_score', 0.0) or 0.0,
                # 添加评分字段
                'trading_frequency_score': (getattr(user_profile.professional_scores, 'trading_behavior_score', 0.0) or 0.0) * 0.25,
                'market_order_ratio_score': (getattr(user_profile.professional_scores, 'trading_behavior_score', 0.0) or 0.0) * 0.25,
                'duration_ratio_score': (getattr(user_profile.professional_scores, 'trading_behavior_score', 0.0) or 0.0) * 0.25,
                'position_consistency_score': (getattr(user_profile.professional_scores, 'trading_behavior_score', 0.0) or 0.0) * 0.25
            },
            'market_understanding': {
                'timing_ability': getattr(user_profile.derived_metrics, 'position_timing_ability', 0.0) or 0.0,
                'risk_discipline': getattr(user_profile.derived_metrics, 'risk_management_discipline', 0.0) or 0.0,
                'execution_efficiency': getattr(user_profile.derived_metrics, 'trading_execution_efficiency', 0.0) or 0.0,
                'total_score': getattr(user_profile.professional_scores, 'market_understanding_score', 0.0) or 0.0,
                # 添加评分字段
                'timing_ability_score': (getattr(user_profile.professional_scores, 'market_understanding_score', 0.0) or 0.0) * 0.33,
                'risk_discipline_score': (getattr(user_profile.professional_scores, 'market_understanding_score', 0.0) or 0.0) * 0.33,
                'execution_efficiency_score': (getattr(user_profile.professional_scores, 'market_understanding_score', 0.0) or 0.0) * 0.34
            }
        }
        
        # 12. 🚀 交易偏好 (trading_preferences) - 前端期望的结构
        trading_preferences = {
            'coin_preference': {
                'mainstream_percentage': coin_preference.get('mainstream_ratio', 0) * 100,
                'altcoin_percentage': coin_preference.get('altcoin_ratio', 0) * 100,
                'defi_percentage': coin_preference.get('defi_ratio', 0) * 100,
                'others_percentage': coin_preference.get('others_ratio', 0) * 100,
                'favorite_contracts': []  # TODO: 从用户偏好数据中提取
            },
            'time_preference': {
                'peak_hours': [time_preference.get('peak_trading_time', '无明显偏好')]
            },
            'risk_preference': {
                'risk_appetite_level': '中等',  # TODO: 基于杠杆和仓位数据计算
                'volatility_preference': '中等',
                'diversification_score': len(coin_win_rate_analysis.get('other_coins_win_rate', {})) / 10.0  # 简单计算
            }
        }
        
        # 13. 🚀 币种分析 (coin_analysis)
        coin_analysis = {
            'advantage_coins': [coin_win_rate_analysis.get('best_performing_coin', '')],
            'coin_expertise_summary': f"共交易{coin_win_rate_analysis.get('total_coins_traded', 0)}个币种",
            'avg_coin_win_rate': (coin_win_rate_analysis.get('btc_win_rate', 0) + coin_win_rate_analysis.get('eth_win_rate', 0)) / 2,
            'coin_performance_ranking': []  # TODO: 从币种分析数据构建
        }
        
        # 14. 🚀 对冲统计 (hedge_statistics)
        hedge_statistics = {
            'hedge_positions_count': user_profile.hedge_statistics.hedge_positions_count if user_profile.hedge_statistics else 0,
            'max_concurrent_positions': user_profile.hedge_statistics.max_concurrent_positions if user_profile.hedge_statistics else 0,
            'concurrent_positions_count': user_profile.hedge_statistics.concurrent_positions_count if user_profile.hedge_statistics else 0,
            'hedge_contracts': user_profile.hedge_statistics.hedge_contracts if user_profile.hedge_statistics else [],
            'avg_hedge_duration': '-- 小时'  # TODO: 计算平均对冲时长
        }
        
        # 15. 🚀 异常分析 (abnormal_analysis) - 使用增强计算
        abnormal_analysis = self._calculate_enhanced_abnormal_analysis_from_profile(user_profile)
        
        # 16. 🚀 风险摘要 (risk_summary)
        risk_summary = {
            'total_risks': user_profile.abnormal_analysis.risk_events_count if user_profile.abnormal_analysis else 0,
            'max_score': user_profile.professional_scores.total_score,
            'total_volume': user_profile.basic_metrics.total_volume,
            'risk_types': '1' if user_profile.abnormal_analysis and user_profile.abnormal_analysis.risk_events_count > 0 else '0',
            'risk_categories': []  # TODO: 分类风险事件
        }
        
        # 17. 🚀 关联分析 (associations) - 占位符
        associations = {
            'association_summary': {
                'total_associated_users': 0,
                'high_risk_associations': 0,
                'medium_risk_associations': 0,
                'low_risk_associations': 0
            },
            'associated_users': [],
            'suspicious_networks': []
        }
        
        # 18. 🚀 交易详情 (transaction_details) - 从数据仓库获取
        try:
            from .unified_data_repository import data_repository
            transaction_details_data = data_repository.get_unified_transaction_details(user_id, task_id)

            transaction_details = {
                'total_transactions': transaction_details_data.get('summary', {}).get('total_transactions', user_profile.basic_metrics.total_trades),
                'total_volume': transaction_details_data.get('summary', {}).get('total_volume', user_profile.basic_metrics.total_volume),
                'transactions': transaction_details_data.get('transactions', []),
                'risk_transactions': transaction_details_data.get('risk_transactions', []),
                'risk_by_type': transaction_details_data.get('risk_by_type', {}),
                'total_risk_transactions': transaction_details_data.get('total_risk_transactions', 0)
            }
        except Exception as e:
            logger.warning(f"获取交易详情失败，使用默认值: {str(e)}")
            transaction_details = {
                'total_transactions': user_profile.basic_metrics.total_trades,
                'total_volume': user_profile.basic_metrics.total_volume,
                'transactions': [],
                'risk_transactions': [],
                'risk_by_type': {},
                'total_risk_transactions': 0
            }

        # 🔧 修复：添加前端期望的基础指标字段名
        # 构建交易规模指标 (trading_scale_metrics)
        trading_scale_metrics = {
            'total_volume': user_profile.basic_metrics.total_volume,
            'total_trades': user_profile.basic_metrics.total_trades,
            'avg_trade_size': user_profile.basic_metrics.avg_trade_size,
            'max_single_trade': user_profile.basic_metrics.max_trade_amount,
            'min_single_trade': user_profile.basic_metrics.min_trade_amount,
            'max_trade_contract': user_profile.basic_metrics.max_trade_contract,
            'min_trade_contract': user_profile.basic_metrics.min_trade_contract,
            'trading_days': user_profile.basic_metrics.total_trading_days
        }
        
        # 构建持仓时间指标 (holding_time_metrics) - 扩展前端期望的详细字段
        holding_time_metrics = {
            'avg_profit_duration': user_profile.basic_metrics.avg_profit_duration,
            'avg_loss_duration': user_profile.basic_metrics.avg_loss_duration,
            'avg_holding_time': (user_profile.basic_metrics.avg_profit_duration + user_profile.basic_metrics.avg_loss_duration) / 2,
            'duration_consistency': user_profile.derived_metrics.profit_loss_duration_ratio if user_profile.derived_metrics else 1.0,
            
            # 🔧 新增：前端期望的详细时间字段
            'avg_profit_duration_hours': user_profile.basic_metrics.avg_profit_duration / 60.0,  # 转换为小时
            'avg_profit_duration_minutes': user_profile.basic_metrics.avg_profit_duration,
            'avg_loss_duration_hours': user_profile.basic_metrics.avg_loss_duration / 60.0,
            'avg_loss_duration_minutes': user_profile.basic_metrics.avg_loss_duration,
            
            # 盈亏时长比和描述
            'duration_ratio': user_profile.derived_metrics.profit_loss_duration_ratio if user_profile.derived_metrics else 1.0,
            'duration_ratio_description': '盈利持仓时间与亏损持仓时间的比值',
            
            # 最长最短持仓时间（模拟数据，实际应从数据库获取）
            'max_holding_time': f"{max(user_profile.basic_metrics.avg_profit_duration, user_profile.basic_metrics.avg_loss_duration) * 2:.0f} 分钟",
            'max_holding_contract': user_profile.basic_metrics.max_trade_contract or 'BTC/USDT',
            'min_holding_time': f"{min(user_profile.basic_metrics.avg_profit_duration, user_profile.basic_metrics.avg_loss_duration) / 2:.0f} 分钟",
            'min_holding_description': '短线交易为主',
            
            # 交易频率
            'trading_frequency': user_profile.derived_metrics.trading_frequency if user_profile.derived_metrics else 0.0,
            'frequency_description': '正常交易频率' if user_profile.derived_metrics and user_profile.derived_metrics.trading_frequency < 10 else '高频交易'
        }
        
        # 构建订单类型指标 (order_type_metrics) - 扩展前端期望的详细字段
        total_orders = (user_profile.basic_metrics.market_orders_open + user_profile.basic_metrics.limit_orders_open + 
                       user_profile.basic_metrics.market_orders_close + user_profile.basic_metrics.limit_orders_close)
        order_type_metrics = {
            # 基础订单数量
            'market_orders_open': user_profile.basic_metrics.market_orders_open,
            'limit_orders_open': user_profile.basic_metrics.limit_orders_open,
            'market_orders_close': user_profile.basic_metrics.market_orders_close,
            'limit_orders_close': user_profile.basic_metrics.limit_orders_close,
            
            # 🔧 新增：前端期望的详细比例字段
            'open_market_ratio': (user_profile.basic_metrics.market_orders_open / max(total_orders, 1)) * 100,
            'open_limit_ratio': (user_profile.basic_metrics.limit_orders_open / max(total_orders, 1)) * 100,
            'close_market_ratio': (user_profile.basic_metrics.market_orders_close / max(total_orders, 1)) * 100,
            'close_limit_ratio': (user_profile.basic_metrics.limit_orders_close / max(total_orders, 1)) * 100,
            
            # 总体比例
            'market_order_ratio': order_type_indicators.get('market_order_ratio', 0),
            'limit_order_ratio': order_type_indicators.get('limit_order_ratio', 0),
            'market_ratio': order_type_indicators.get('market_order_ratio', 0),
            'limit_ratio': order_type_indicators.get('limit_order_ratio', 0),
            
            # 描述性文字
            'market_order_description': '偏好市价单交易' if order_type_indicators.get('market_order_ratio', 0) > 50 else '偏好限价单交易',
            'limit_order_description': '善于使用限价单控制成本' if order_type_indicators.get('limit_order_ratio', 0) > 50 else '更多使用市价单快速成交'
        }
        
        # 构建风险控制指标 (risk_control_metrics)
        risk_control_metrics = {
            'avg_leverage': leverage_indicators.get('avg_leverage', 0),
            'max_leverage': leverage_indicators.get('max_leverage', 0),
            'leverage_stability': leverage_indicators.get('leverage_stability', 0),
            'max_single_loss': user_profile.derived_metrics.max_single_loss if user_profile.derived_metrics else 0.0,
            'max_single_loss_ratio': user_profile.derived_metrics.max_single_loss_ratio if user_profile.derived_metrics else 0.0,
            'risk_score': user_profile.professional_scores.risk_control_score
        }
        
        # 构建资金规模指标 (fund_scale_metrics) - 扩展前端期望的详细字段
        estimated_capital = user_profile.basic_metrics.total_volume / 10  # 简单估算
        total_trades = user_profile.basic_metrics.total_trades
        
        # 模拟交易规模分布（实际应从position_size_distribution获取）
        size_dist = user_profile.basic_metrics.position_size_distribution or {}
        small_trades = int(total_trades * size_dist.get('small', 0.3))
        medium_trades = int(total_trades * size_dist.get('medium', 0.5))
        large_trades = int(total_trades * size_dist.get('large', 0.2))
        
        fund_scale_metrics = {
            'fund_scale_range': user_profile.fund_scale_category,
            'estimated_fund_size': estimated_capital,
            'scale_category': user_profile.fund_scale_category,
            'volume_distribution': position_size_indicators.get('size_distribution', {}),
            
            # 🔧 新增：前端期望的详细字段
            'estimated_capital': estimated_capital,
            'fund_scale_category': user_profile.fund_scale_category,
            
            # 交易规模分布
            'small_trades': small_trades,
            'medium_trades': medium_trades,
            'large_trades': large_trades,
            'small_trades_ratio': size_dist.get('small', 0.3) * 100,
            'medium_trades_ratio': size_dist.get('medium', 0.5) * 100,
            'large_trades_ratio': size_dist.get('large', 0.2) * 100,
            
            # 仓位一致性
            'position_consistency': user_profile.derived_metrics.position_size_consistency if user_profile.derived_metrics else 0.8,
            'position_consistency_description': '仓位规模较为一致' if (user_profile.derived_metrics and user_profile.derived_metrics.position_size_consistency > 0.7) else '仓位规模变化较大'
        }

        # 🔧 关键修复：添加前端期望的 basic_metrics 字段
        basic_metrics = {
            # 盈亏统计字段 - 前端 fillAdditionalBasicMetrics 方法需要的字段
            'profitable_count': user_profile.basic_metrics.profitable_count,
            'loss_count': user_profile.basic_metrics.loss_count,
            'total_profit': user_profile.basic_metrics.total_profit,
            'total_loss': user_profile.basic_metrics.total_loss,
            'total_fees': user_profile.basic_metrics.total_commission,  # 🔧 修正：使用 total_commission
            'total_volume': user_profile.basic_metrics.total_volume,
            'total_trades': user_profile.basic_metrics.total_trades,
            'avg_trade_size': user_profile.basic_metrics.avg_trade_size,
            
            # 扩展字段 - 来自扩展指标整合
            'max_trade_amount': user_profile.basic_metrics.max_trade_amount,
            'max_trade_contract': user_profile.basic_metrics.max_trade_contract,
            'min_trade_amount': user_profile.basic_metrics.min_trade_amount,
            'min_trade_contract': user_profile.basic_metrics.min_trade_contract,
            
            # 订单类型字段
            'market_orders_open': user_profile.basic_metrics.market_orders_open,
            'limit_orders_open': user_profile.basic_metrics.limit_orders_open,
            'market_orders_close': user_profile.basic_metrics.market_orders_close,
            'limit_orders_close': user_profile.basic_metrics.limit_orders_close,
            
            # 持仓时间字段
            'avg_profit_duration': user_profile.basic_metrics.avg_profit_duration,
            'avg_loss_duration': user_profile.basic_metrics.avg_loss_duration,
            
            # 分布数据
            'coin_preference_distribution': user_profile.basic_metrics.coin_preference_distribution,
            'time_distribution': user_profile.basic_metrics.time_distribution,
            'leverage_distribution': user_profile.basic_metrics.leverage_distribution,
            'order_type_distribution': user_profile.basic_metrics.order_type_distribution,
            'position_size_distribution': user_profile.basic_metrics.position_size_distribution
        }

        # 组装最终结果
        frontend_data = {
            'search_area': search_area,
            'user_profile': user_profile_data,
            'professional_dashboard': professional_dashboard,
            'pnl_metrics': pnl_metrics,
            'coin_win_rate_analysis': coin_win_rate_analysis,
            'coin_preference': coin_preference,
            'time_preference': time_preference,
            'leverage_indicators': leverage_indicators,
            'order_type_indicators': order_type_indicators,
            'position_size_indicators': position_size_indicators,
            
            # 🔧 关键修复：添加前端期望的 basic_metrics 字段
            'basic_metrics': basic_metrics,
            
            # 🔧 新增：前端期望的基础指标字段
            'trading_scale_metrics': trading_scale_metrics,
            'holding_time_metrics': holding_time_metrics,
            'order_type_metrics': order_type_metrics,
            'risk_control_metrics': risk_control_metrics,
            'fund_scale_metrics': fund_scale_metrics,
            
            # 🚀 新增前端期望的数据结构
            'derived_analysis': derived_analysis,
            'trading_preferences': trading_preferences,
            'coin_analysis': coin_analysis,
            'hedge_statistics': hedge_statistics,
            'abnormal_analysis': abnormal_analysis,
            'risk_summary': risk_summary,
            'associations': associations,
            'transaction_details': transaction_details,
            
            # 元数据
            'metadata': {
                'analysis_timestamp': user_profile.analysis_timestamp.isoformat(),
                'analysis_period': {
                    'start': user_profile.analysis_period_start.isoformat(),
                    'end': user_profile.analysis_period_end.isoformat()
                },
                'data_quality_score': user_profile.data_quality_score,
                'analysis_confidence': user_profile.analysis_confidence,
                'mapping_version': '1.0.0'
            }
        }
        
        logger.info(f"用户 {user_profile.user_id} 的数据映射完成，包含 {len(frontend_data)} 个主要分类")
        return frontend_data
    
    def _get_best_performing_coin(self, coin_analysis: Dict) -> str:
        """获取表现最好的币种"""
        if not coin_analysis:
            return ""
        
        best_coin = max(coin_analysis.items(), key=lambda x: x[1].win_rate)
        return best_coin[0]
    
    def _get_worst_performing_coin(self, coin_analysis: Dict) -> str:
        """获取表现最差的币种"""
        if not coin_analysis:
            return ""
        
        worst_coin = min(coin_analysis.items(), key=lambda x: x[1].win_rate)
        return worst_coin[0]
    
    def _generate_coin_preference_description(self, coin_pref_dist: Dict) -> str:
        """生成币种偏好描述"""
        if not coin_pref_dist:
            return "数据不足"
        
        mainstream = coin_pref_dist.get('mainstream', 0.0)
        altcoin = coin_pref_dist.get('altcoin', 0.0)
        defi = coin_pref_dist.get('defi', 0.0)
        others = coin_pref_dist.get('others', 0.0)
        
        if mainstream > 0.5:
            return "偏好主流币种交易"
        elif altcoin > 0.4:
            return "偏好山寨币交易"
        elif defi > 0.3:
            return "偏好DeFi代币交易"
        else:
            return "交易币种较为分散"

    def _calculate_performance_score_from_data(self, coin_data: Dict) -> float:
        """从币种数据计算性能评分"""
        try:
            win_rate = coin_data.get('win_rate', 0)
            profit_factor = coin_data.get('profit_factor', 0)
            total_volume = coin_data.get('total_volume', 0)
            total_trades = coin_data.get('total_trades', 0)

            # 胜率评分 (0-100)
            win_rate_score = min(100, win_rate * 100)

            # 盈利因子评分 (0-100)
            if profit_factor >= 999 or profit_factor == float('inf'):
                profit_factor_score = 100  # 🚀 修复：处理极大值和无穷大
            else:
                profit_factor_score = min(100, max(0, (profit_factor - 1) * 25))

            # 交易量评分 (0-100) - 对数缩放
            import math
            volume_score = min(100, math.log10(max(1, total_volume)) * 10)

            # 交易笔数评分 (0-100)
            trades_score = min(100, total_trades * 5)

            # 加权综合评分
            total_score = (
                win_rate_score * 0.4 +
                profit_factor_score * 0.3 +
                volume_score * 0.2 +
                trades_score * 0.1
            )

            return round(total_score, 2)
        except Exception as e:
            logger.warning(f"计算性能评分失败: {e}")
            return 0.0

    def _get_peak_trading_time(self, time_dist: Dict) -> str:
        """获取峰值交易时间"""
        if not time_dist:
            return "数据不足"
        
        # 找到交易量最大的时间段
        peak_hour = max(time_dist.items(), key=lambda x: x[1])
        hour = int(peak_hour[0])
        
        if 0 <= hour < 6:
            return "凌晨时段"
        elif 6 <= hour < 12:
            return "上午时段"
        elif 12 <= hour < 18:
            return "下午时段"
        else:
            return "晚间时段"

    def map_user_trading_profile_to_frontend(self, profile_data: Dict, task_id: str = None) -> Dict:
        """
        🚀 将user_trading_profiles表的数据直接映射为前端需要的数据格式
        这是优化后的方案 - 直接使用数据库中的完整数据
        
        Args:
            profile_data: 从user_trading_profiles表查询的数据
            task_id: 任务ID
            
        Returns:
            Dict: 前端格式的数据
        """
        logger.info(f"开始将用户 {profile_data.get('member_id')} 的数据库数据映射为前端格式")
        
        user_id = profile_data.get('member_id')
        analysis_date = profile_data.get('analysis_date')
        
        # 🔧 添加数据类型转换辅助函数
        def safe_float(value, default=0.0):
            """安全转换为浮点数"""
            if value is None:
                return default
            try:
                return float(value)
            except (ValueError, TypeError):
                return default
        
        def safe_int(value, default=0):
            """安全转换为整数"""
            if value is None:
                return default
            try:
                return int(float(value))
            except (ValueError, TypeError):
                return default
        
        # 1. 搜索区域数据 (search_area)
        search_area = {
            'member_id': user_id,
            'digital_id': f"D{user_id}",
            'task_id': task_id or f"TASK_{user_id}_{int(datetime.now().timestamp())}",
            'contract_tasks': [],  # TODO: 需要查询任务表
            'agent_tasks': [],     # TODO: 需要查询任务表
            'search_method': '数据库查询',
            'search_time': datetime.now().isoformat(),
            'data_source': 'user_trading_profiles表'
        }
        
        # 2. 用户档案数据 (user_profile)
        user_profile_data = {
            'member_id': user_id,
            'digital_id': f"D{user_id}",
            'user_type': '普通用户',
            'bd_name': '',  
            'total_risk_events': profile_data.get('risk_events_count', 0),
            'last_activity': profile_data.get('last_activity_time', '') or profile_data.get('updated_at', ''),
            'search_method': '数据库查询',
            'analysis_status': '完成'
        }
        
        # 3. 专业度仪表盘 (professional_dashboard)
        professional_dashboard = {
            'total_score': safe_float(profile_data.get('professional_score'), 0),
            'profitability_score': safe_float(profile_data.get('profitability_score'), 0),
            'risk_control_score': safe_float(profile_data.get('risk_control_score'), 0),
            'trading_behavior_score': safe_float(profile_data.get('trading_behavior_score'), 0),
            'market_understanding_score': safe_float(profile_data.get('market_understanding_score'), 0),
            'trader_type': profile_data.get('trader_type', '数据不足'),
            'fund_scale_category': profile_data.get('fund_scale_category', '数据不足'),
            'data_quality_score': 0.95  # 来自数据库的数据质量较高
        }
        
        # 4. 盈亏指标 (pnl_metrics) 🔧 修复：使用安全数值转换，添加手续费字段
        pnl_metrics = {
            'win_rate': safe_float(profile_data.get('win_rate'), 0),
            'total_pnl': safe_float(profile_data.get('total_pnl'), 0),
            'total_profit': safe_float(profile_data.get('total_profit'), 0),
            'total_loss': safe_float(profile_data.get('total_loss'), 0),
            'profit_loss_ratio': safe_float(profile_data.get('profit_loss_ratio'), 0),
            'profit_factor': safe_float(profile_data.get('profit_factor'), 0),
            'profitable_trades': safe_int(profile_data.get('profitable_count'), 0),
            'loss_trades': safe_int(profile_data.get('loss_count'), 0),
            'avg_profit_per_trade': safe_float(profile_data.get('avg_profit_per_trade'), 0),
            'avg_loss_per_trade': safe_float(profile_data.get('avg_loss_per_trade'), 0),
            'max_profit': 0.0,  # TODO: 需要从详细数据计算
            'max_loss': safe_float(profile_data.get('max_single_loss'), 0),

            # 🚀 新增：手续费字段
            'total_fees': safe_float(profile_data.get('total_commission'), 0),
            'total_commission': safe_float(profile_data.get('total_commission'), 0),
            'fee_ratio': safe_float(profile_data.get('fee_ratio'), 0)
        }
        
        # 5. 交易规模指标 (trading_scale_metrics) 🔧 修复：使用安全数值转换，添加交易规模分布
        trading_scale_metrics = {
            'total_volume': safe_float(profile_data.get('total_volume'), 0),
            'total_trades': safe_int(profile_data.get('total_trades'), 0),
            'avg_trade_size': safe_float(profile_data.get('avg_trade_size'), 0),
            'max_single_trade': safe_float(profile_data.get('max_trade_size'), 0),
            'min_single_trade': safe_float(profile_data.get('min_trade_size'), 0),
            'max_trade_contract': '',  # TODO: 需要从详细数据获取
            'min_trade_contract': '',  # TODO: 需要从详细数据获取
            'trading_days': safe_int(profile_data.get('total_trading_days'), 0),

            # 🚀 新增：交易规模分布字段
            'small_trades': safe_int(profile_data.get('small_trades'), 0),
            'medium_trades': safe_int(profile_data.get('medium_trades'), 0),
            'large_trades': safe_int(profile_data.get('large_trades'), 0),
            'small_trades_ratio': safe_float(profile_data.get('small_trades_ratio'), 0),
            'medium_trades_ratio': safe_float(profile_data.get('medium_trades_ratio'), 0),
            'large_trades_ratio': safe_float(profile_data.get('large_trades_ratio'), 0)
        }
        
        # 6. 持仓时间指标 (holding_time_metrics) 🔧 修复：使用安全数值转换
        holding_time_metrics = {
            'avg_profit_duration': safe_int(profile_data.get('avg_profit_duration_minutes'), 0),
            'avg_loss_duration': safe_int(profile_data.get('avg_loss_duration_minutes'), 0),
            'duration_ratio': safe_float(profile_data.get('profit_loss_duration_ratio'), 0),
            'max_holding_time': safe_int(profile_data.get('max_holding_time'), 0),
            'min_holding_time': safe_int(profile_data.get('min_holding_time'), 0),
            'trading_frequency': safe_float(profile_data.get('trading_frequency'), 0)
        }
        
        # 7. 订单类型指标 (order_type_metrics) 🔧 修复：使用安全数值转换
        order_type_metrics = {
            'market_order_ratio': safe_float(profile_data.get('market_orders_ratio'), 0),
            'limit_order_ratio': safe_float(profile_data.get('limit_orders_ratio'), 0),
            'open_market_orders': safe_int(profile_data.get('open_market_orders'), 0),
            'open_limit_orders': safe_int(profile_data.get('open_limit_orders'), 0),
            'close_market_orders': safe_int(profile_data.get('close_market_orders'), 0),
            'close_limit_orders': safe_int(profile_data.get('close_limit_orders'), 0)
        }
        
        # 8. 风险控制指标 (risk_control_metrics) 🔧 修复：使用安全数值转换
        risk_control_metrics = {
            'avg_leverage': safe_float(profile_data.get('avg_leverage'), 0),
            'max_leverage': safe_float(profile_data.get('max_leverage'), 0),
            'leverage_stability': safe_float(profile_data.get('leverage_stability'), 0),
            'max_single_loss': safe_float(profile_data.get('max_single_loss'), 0),
            'max_single_loss_ratio': safe_float(profile_data.get('max_single_loss_ratio'), 0),
            'low_leverage_trades': safe_int(profile_data.get('low_leverage_trades'), 0),
            'medium_leverage_trades': safe_int(profile_data.get('medium_leverage_trades'), 0),
            'high_leverage_trades': safe_int(profile_data.get('high_leverage_trades'), 0)
        }
        
        # 9. 资金规模指标 (fund_scale_metrics) 🔧 修复：使用安全数值转换，添加交易规模分布
        fund_scale_metrics = {
            'fund_scale_category': profile_data.get('fund_scale_category', '数据不足'),
            'real_trading_volume': safe_float(profile_data.get('real_trading_volume'), 0),

            # 🚀 新增：交易规模分布字段
            'small_trades': safe_int(profile_data.get('small_trades'), 0),
            'medium_trades': safe_int(profile_data.get('medium_trades'), 0),
            'large_trades': safe_int(profile_data.get('large_trades'), 0),
            'small_trades_ratio': safe_float(profile_data.get('small_trades_ratio'), 0),
            'medium_trades_ratio': safe_float(profile_data.get('medium_trades_ratio'), 0),
            'large_trades_ratio': safe_float(profile_data.get('large_trades_ratio'), 0)
        }
        
        # 10. 衍生分析 (derived_analysis) - 4个维度 🔧 修复：使用安全数值转换
        derived_analysis = {
            'profitability': {
                'win_rate': safe_float(profile_data.get('win_rate'), 0),
                'profit_loss_ratio': safe_float(profile_data.get('profit_loss_ratio'), 0),
                'profit_factor': safe_float(profile_data.get('profit_factor'), 0),
                'consistency': safe_float(profile_data.get('profit_consistency'), 0),
                'profit_consistency': safe_float(profile_data.get('profit_consistency'), 0),
                'total_score': safe_float(profile_data.get('profitability_score'), 0),
                'win_rate_score': safe_float(profile_data.get('win_rate_score'), 0),
                'profit_loss_ratio_score': safe_float(profile_data.get('profit_loss_ratio_score'), 0),
                'profit_factor_score': safe_float(profile_data.get('profit_factor_score'), 0),
                'consistency_score': safe_float(profile_data.get('profit_consistency_score'), 0)
            },
            'risk_control': {
                'avg_leverage': safe_float(profile_data.get('avg_leverage'), 0),
                'max_leverage': safe_float(profile_data.get('max_leverage'), 0),
                'leverage_stability': safe_float(profile_data.get('leverage_stability'), 0),
                'max_single_loss': safe_float(profile_data.get('max_single_loss'), 0),
                'total_score': safe_float(profile_data.get('risk_control_score'), 0),
                'avg_leverage_score': safe_float(profile_data.get('avg_leverage_score'), 0),
                'max_leverage_score': safe_float(profile_data.get('max_leverage_score'), 0),
                'leverage_stability_score': safe_float(profile_data.get('leverage_stability_score'), 0),
                'max_single_loss_score': safe_float(profile_data.get('max_single_loss_score'), 0)
            },
            'trading_behavior': {
                'trading_frequency': safe_float(profile_data.get('trading_frequency'), 0),
                'market_order_ratio': safe_float(profile_data.get('market_orders_ratio'), 0),
                'duration_ratio': safe_float(profile_data.get('profit_loss_duration_ratio'), 0),
                'position_consistency': safe_float(profile_data.get('position_consistency'), 0),
                'total_score': safe_float(profile_data.get('trading_behavior_score'), 0),
                'trading_frequency_score': safe_float(profile_data.get('trading_frequency_score'), 0),
                'market_order_ratio_score': safe_float(profile_data.get('market_order_ratio_score'), 0),
                'duration_ratio_score': safe_float(profile_data.get('duration_ratio_score'), 0),
                'position_consistency_score': safe_float(profile_data.get('position_consistency_score'), 0)
            },
            'market_understanding': {
                'timing_ability': safe_float(profile_data.get('timing_ability'), 0),
                'risk_discipline': safe_float(profile_data.get('risk_discipline'), 0),
                'execution_efficiency': safe_float(profile_data.get('execution_efficiency'), 0),
                'total_score': safe_float(profile_data.get('market_understanding_score'), 0),
                'timing_ability_score': safe_float(profile_data.get('timing_ability_score'), 0),
                'risk_discipline_score': safe_float(profile_data.get('risk_discipline_score'), 0),
                'execution_efficiency_score': safe_float(profile_data.get('execution_efficiency_score'), 0)
            }
        }
        
        # 11. 交易偏好 (trading_preferences) 🔧 修复：使用安全数值转换
        trading_preferences = {
            'coin_preference': {
                'mainstream_percentage': safe_float(profile_data.get('major_coins_ratio'), 0) * 100,
                'altcoin_percentage': safe_float(profile_data.get('altcoins_ratio'), 0) * 100,
                'defi_percentage': safe_float(profile_data.get('defi_percentage'), 0) * 100,
                'others_percentage': safe_float(profile_data.get('others_percentage'), 0) * 100,
                'favorite_contracts': self._parse_json_field(profile_data.get('favorite_contracts', '[]'))
            },
            'time_preference': {
                'peak_hours': self._parse_json_field(profile_data.get('peak_trading_hours', '[]'))
            },
            'risk_preference': {
                'risk_appetite_level': profile_data.get('risk_appetite_level', 'medium'),
                'volatility_preference': safe_float(profile_data.get('volatility_preference'), 0),
                'diversification_score': safe_float(profile_data.get('diversification_score'), 0)
            }
        }
        
        # 12. 币种分析 (coin_analysis) 🔧 修复：正确构建coin_performance_ranking
        coin_win_rate_data = self._parse_json_field(profile_data.get('coin_win_rate_analysis', '{}'))
        advantage_coins = self._parse_json_field(profile_data.get('advantage_coins', '[]'))
        expert_coins = self._parse_json_field(profile_data.get('expert_coins', '[]'))

        # 🚀 从coin_win_rate_analysis构建coin_performance_ranking
        coin_performance_ranking = []
        if coin_win_rate_data:
            # 将币种数据转换为排行榜格式
            for contract, coin_data in coin_win_rate_data.items():
                ranking_item = {
                    'rank': coin_data.get('performance_rank', 0),
                    'contract': contract,
                    'win_rate': coin_data.get('win_rate', 0),
                    'total_trades': coin_data.get('total_trades', 0),
                    'net_pnl': coin_data.get('net_pnl', 0),
                    'profit_factor': coin_data.get('profit_factor', 0),
                    'expertise_level': coin_data.get('expertise_level', '新手级'),
                    'performance_score': self._calculate_performance_score_from_data(coin_data)
                }
                coin_performance_ranking.append(ranking_item)

            # 按performance_rank排序
            coin_performance_ranking.sort(key=lambda x: x['rank'] if x['rank'] > 0 else 999)

        coin_analysis = {
            'coin_win_rates': coin_win_rate_data,  # 原始币种数据
            'coin_performance_ranking': coin_performance_ranking,  # 🚀 前端期望的排行榜格式
            'advantage_coins': advantage_coins,
            'expert_coins': expert_coins,
            'total_analyzed_coins': safe_int(profile_data.get('total_analyzed_coins'), 0),
            'analysis_coins_count': safe_int(profile_data.get('total_analyzed_coins'), 0),  # 前端期望的字段名
            'avg_coin_win_rate': safe_float(profile_data.get('avg_coin_win_rate'), 0),
            'advantage_coins_count': len(advantage_coins),
            'expert_coins_count': len(expert_coins)
        }
        
        # 13. 对冲统计 (hedge_statistics) 🔧 修复：使用安全数值转换
        hedge_statistics = {
            'hedge_positions_count': safe_int(profile_data.get('hedge_positions_count'), 0),
            'concurrent_positions_count': safe_int(profile_data.get('concurrent_positions_count'), 0),
            'hedge_contracts': self._parse_json_field(profile_data.get('hedge_contracts', '[]'))
        }
        
        # 14. 异常分析 (abnormal_analysis) 🔧 修复：使用安全数值转换 + 从contract_risk_details计算缺失字段
        abnormal_analysis = self._calculate_enhanced_abnormal_analysis(profile_data)
        
        # 15. 关联分析 (associations) - 占位符
        associations = {
            'association_summary': {
                'total_associated_users': 0,
                'high_risk_associations': 0,
                'medium_risk_associations': 0,
                'low_risk_associations': 0
            },
            'associated_users': [],
            'suspicious_networks': []
        }
        
        # 16. 交易详情 (transaction_details) 🔧 修复：从数据仓库获取详细交易记录
        try:
            from .unified_data_repository import data_repository
            transaction_details_data = data_repository.get_unified_transaction_details(member_id, task_id)

            transaction_details = {
                'total_transactions': transaction_details_data.get('summary', {}).get('total_transactions', safe_int(profile_data.get('total_trades'), 0)),
                'total_volume': transaction_details_data.get('summary', {}).get('total_volume', safe_float(profile_data.get('total_volume'), 0)),
                'transactions': transaction_details_data.get('transactions', []),
                'risk_transactions': transaction_details_data.get('risk_transactions', []),
                'risk_by_type': transaction_details_data.get('risk_by_type', {}),
                'total_risk_transactions': transaction_details_data.get('total_risk_transactions', 0)
            }
        except Exception as e:
            logger.warning(f"获取交易详情失败，使用默认值: {str(e)}")
            transaction_details = {
                'total_transactions': safe_int(profile_data.get('total_trades'), 0),
                'total_volume': safe_float(profile_data.get('total_volume'), 0),
                'transactions': [],
                'risk_transactions': [],
                'risk_by_type': {},
                'total_risk_transactions': 0
            }
        
        # 添加风险摘要 (risk_summary) 
        risk_summary = {
            'total_risks': safe_int(profile_data.get('risk_events_count'), 0),
            'max_score': safe_float(profile_data.get('professional_score'), 0),
            'total_volume': safe_float(profile_data.get('total_volume'), 0),
            'risk_types': '1' if safe_int(profile_data.get('risk_events_count'), 0) > 0 else '0',
            'risk_categories': []  # TODO: 分类风险事件
        }
        
        # 17. 元数据 (metadata) 🔧 修复：使用安全数值转换
        metadata = {
            'analysis_timestamp': profile_data.get('updated_at', datetime.now().isoformat()),
            'data_quality_score': 0.95,  # 数据库数据质量高
            'analysis_confidence': safe_float(profile_data.get('confidence_level'), 0.85),
            'mapping_version': 'v2.0_database_direct',
            'data_source': 'user_trading_profiles表',
            'field_count': len([k for k, v in profile_data.items() if v not in [None, 0, '', '[]', '{}']])
        }
        
        # 🚀 添加专业度评分数据 (professional_scores) - 前端期望的格式
        professional_scores = {
            'total_score': safe_float(profile_data.get('professional_score'), 0),
            'profitability_score': safe_float(profile_data.get('profitability_score'), 0),
            'risk_control_score': safe_float(profile_data.get('risk_control_score'), 0),
            'trading_behavior_score': safe_float(profile_data.get('trading_behavior_score'), 0),
            'market_understanding_score': safe_float(profile_data.get('market_understanding_score'), 0),
            'trader_type': profile_data.get('trader_type', '数据不足'),
            'confidence_level': safe_float(profile_data.get('confidence_level'), 0.85)
        }

        # 构建最终的前端数据格式
        frontend_data = {
            'search_area': search_area,
            'user_profile': user_profile_data,
            'professional_dashboard': professional_dashboard,
            'professional_scores': professional_scores,  # 🚀 新增：前端期望的专业度评分格式
            'pnl_metrics': pnl_metrics,
            'trading_scale_metrics': trading_scale_metrics,
            'holding_time_metrics': holding_time_metrics,
            'order_type_metrics': order_type_metrics,
            'risk_control_metrics': risk_control_metrics,
            'fund_scale_metrics': fund_scale_metrics,
            'derived_analysis': derived_analysis,
            'trading_preferences': trading_preferences,
            'coin_analysis': coin_analysis,
            'hedge_statistics': hedge_statistics,
            'abnormal_analysis': abnormal_analysis,
            'risk_summary': risk_summary,
            'associations': associations,
            'transaction_details': transaction_details,
            'metadata': metadata
        }
        
        logger.info(f"用户 {user_id} 的数据库数据映射完成，包含 {len(frontend_data)} 个数据分类")
        return frontend_data
    
    def _parse_json_field(self, json_str: str) -> any:
        """
        安全解析JSON字段
        🔧 修复：对于 advantage_coins 和 favorite_contracts 等数组字段，
        当输入为 None 或无效时，始终返回空数组而不是空字典
        """
        if not json_str or json_str in ['', 'null', '{}', '[]']:
            # 🔧 修复：对于空值，默认返回空数组（适用于大多数前端期望的数组字段）
            return []

        try:
            import json
            result = json.loads(json_str)
            return result
        except:
            # 🔧 修复：解析失败时，根据字符串格式判断应该返回数组还是对象
            if json_str.startswith('[') or json_str.startswith('"'):
                return []  # 数组格式或字符串格式，返回空数组
            else:
                return {}  # 对象格式，返回空对象

    def _calculate_enhanced_abnormal_analysis(self, profile_data: Dict) -> Dict:
        """
        🚀 计算增强的异常交易分析数据
        从contract_risk_details表中获取详细的异常交易笔数和比例
        """
        try:
            member_id = profile_data.get('member_id')
            if not member_id:
                return self._get_default_abnormal_analysis(profile_data)

            # 从contract_risk_details表查询详细的异常交易数据
            abnormal_details = self._query_abnormal_trading_details(member_id)

            # 基础数据
            total_volume = safe_float(profile_data.get('total_volume'), 0)
            wash_trading_volume = safe_float(profile_data.get('wash_trading_volume'), 0)
            high_frequency_volume = safe_float(profile_data.get('high_frequency_volume'), 0)
            funding_arbitrage_volume = safe_float(profile_data.get('funding_arbitrage_volume'), 0)

            # 从详细数据中提取笔数
            wash_trading_count = abnormal_details.get('wash_trading_count', 0)
            high_frequency_count = abnormal_details.get('high_frequency_count', 0)
            arbitrage_count = abnormal_details.get('arbitrage_count', 0)

            # 计算比例（相对于总交易量）
            wash_trading_ratio = (wash_trading_volume / total_volume) if total_volume > 0 else 0
            high_freq_ratio = (high_frequency_volume / total_volume) if total_volume > 0 else 0
            funding_arbitrage_ratio = (funding_arbitrage_volume / total_volume) if total_volume > 0 else 0

            # 🔧 修复：添加前端期望的字段名
            abnormal_volume = safe_float(profile_data.get('abnormal_volume'), 0)

            return {
                'total_abnormal_volume': abnormal_volume,
                'abnormal_volume': abnormal_volume,  # 🔧 修复：前端期望的字段名
                'abnormal_ratio': safe_float(profile_data.get('abnormal_ratio'), 0),
                'wash_trading_volume': wash_trading_volume,
                'wash_trading_count': wash_trading_count,
                'wash_trading_ratio': wash_trading_ratio,
                'high_frequency_volume': high_frequency_volume,
                'high_freq_volume': high_frequency_volume,  # 兼容字段
                'high_frequency_count': high_frequency_count,
                'high_freq_ratio': high_freq_ratio,
                'funding_arbitrage_volume': funding_arbitrage_volume,
                'arbitrage_count': arbitrage_count,
                'funding_arbitrage_ratio': funding_arbitrage_ratio,
                'risk_events_count': safe_int(profile_data.get('risk_events_count'), 0),
                'real_trading_scale': safe_float(profile_data.get('real_trading_volume'), 0)
            }

        except Exception as e:
            logger.warning(f"计算增强异常分析失败: {e}")
            return self._get_default_abnormal_analysis(profile_data)

    def _query_abnormal_trading_details(self, member_id: str) -> Dict:
        """
        从contract_risk_details表查询用户的异常交易详细数据
        """
        try:
            from database.duckdb_manager import DuckDBManager

            db_manager = DuckDBManager()

            # 查询用户的异常交易记录，按类型分组统计
            sql = """
            SELECT
                detection_type,
                COUNT(*) as count,
                SUM(COALESCE(abnormal_volume, 0)) as total_volume,
                AVG(COALESCE(risk_score, 0)) as avg_risk_score
            FROM contract_risk_details
            WHERE member_id = ?
            GROUP BY detection_type
            """

            results = db_manager.execute_sql(sql, [member_id])

            # 初始化计数器
            details = {
                'wash_trading_count': 0,
                'high_frequency_count': 0,
                'arbitrage_count': 0,
                'total_abnormal_count': 0
            }

            # 异常类型映射
            type_mapping = {
                'wash_trading': 'wash_trading_count',
                'suspected_wash_trading': 'wash_trading_count',
                'same_account_wash_trading': 'wash_trading_count',
                'cross_account_wash_trading': 'wash_trading_count',
                'high_frequency_trading': 'high_frequency_count',
                'funding_rate_arbitrage': 'arbitrage_count'
            }

            # 统计各类型的笔数
            for row in results:
                detection_type = row.get('detection_type', '').lower()
                count = int(row.get('count', 0))

                # 映射到对应的计数器
                if detection_type in type_mapping:
                    field_name = type_mapping[detection_type]
                    details[field_name] += count

                details['total_abnormal_count'] += count

            logger.debug(f"用户 {member_id} 异常交易详情: {details}")
            return details

        except Exception as e:
            logger.warning(f"查询用户 {member_id} 异常交易详情失败: {e}")
            return {
                'wash_trading_count': 0,
                'high_frequency_count': 0,
                'arbitrage_count': 0,
                'total_abnormal_count': 0
            }

    def _get_default_abnormal_analysis(self, profile_data: Dict) -> Dict:
        """获取默认的异常分析数据"""
        # 🔧 修复：添加前端期望的字段名
        abnormal_volume = safe_float(profile_data.get('abnormal_volume'), 0)

        return {
            'total_abnormal_volume': abnormal_volume,
            'abnormal_volume': abnormal_volume,  # 🔧 修复：前端期望的字段名
            'abnormal_ratio': safe_float(profile_data.get('abnormal_ratio'), 0),
            'wash_trading_volume': safe_float(profile_data.get('wash_trading_volume'), 0),
            'wash_trading_count': 0,
            'wash_trading_ratio': 0,
            'high_frequency_volume': safe_float(profile_data.get('high_frequency_volume'), 0),
            'high_freq_volume': safe_float(profile_data.get('high_frequency_volume'), 0),
            'high_frequency_count': 0,
            'high_freq_ratio': 0,
            'funding_arbitrage_volume': safe_float(profile_data.get('funding_arbitrage_volume'), 0),
            'arbitrage_count': 0,
            'funding_arbitrage_ratio': 0,
            'risk_events_count': safe_int(profile_data.get('risk_events_count'), 0),
            'real_trading_scale': safe_float(profile_data.get('real_trading_volume'), 0)
        }

    def _calculate_enhanced_abnormal_analysis_from_profile(self, user_profile) -> Dict:
        """
        🚀 从UserBehaviorProfile对象计算增强的异常交易分析数据
        """
        try:
            if not user_profile or not user_profile.abnormal_analysis:
                return self._get_empty_abnormal_analysis()

            abnormal = user_profile.abnormal_analysis
            basic = user_profile.basic_metrics

            # 从contract_risk_details表查询详细数据
            abnormal_details = self._query_abnormal_trading_details(user_profile.user_id)

            # 基础数据
            total_volume = basic.total_volume if basic else 0
            wash_trading_volume = abnormal.wash_trading_volume
            high_frequency_volume = abnormal.high_frequency_volume
            funding_arbitrage_volume = abnormal.funding_arbitrage_volume

            # 从详细数据中提取笔数
            wash_trading_count = abnormal_details.get('wash_trading_count', 0)
            high_frequency_count = abnormal_details.get('high_frequency_count', 0)
            arbitrage_count = abnormal_details.get('arbitrage_count', 0)

            # 计算比例（相对于总交易量）
            wash_trading_ratio = (wash_trading_volume / total_volume) if total_volume > 0 else 0
            high_freq_ratio = (high_frequency_volume / total_volume) if total_volume > 0 else 0
            funding_arbitrage_ratio = (funding_arbitrage_volume / total_volume) if total_volume > 0 else 0

            return {
                'total_abnormal_volume': abnormal.abnormal_volume,
                'abnormal_ratio': abnormal.abnormal_ratio,
                'wash_trading_volume': wash_trading_volume,
                'wash_trading_count': wash_trading_count,
                'wash_trading_ratio': wash_trading_ratio,
                'high_frequency_volume': high_frequency_volume,
                'high_freq_volume': high_frequency_volume,  # 兼容字段
                'high_frequency_count': high_frequency_count,
                'high_freq_ratio': high_freq_ratio,
                'funding_arbitrage_volume': funding_arbitrage_volume,
                'arbitrage_count': arbitrage_count,
                'funding_arbitrage_ratio': funding_arbitrage_ratio,
                'risk_events_count': abnormal.risk_events_count,
                'real_trading_scale': basic.real_trading_volume if basic else 0
            }

        except Exception as e:
            logger.warning(f"从profile计算增强异常分析失败: {e}")
            return self._get_empty_abnormal_analysis()

    def _get_empty_abnormal_analysis(self) -> Dict:
        """获取空的异常分析数据"""
        return {
            'total_abnormal_volume': 0,
            'abnormal_ratio': 0,
            'wash_trading_volume': 0,
            'wash_trading_count': 0,
            'wash_trading_ratio': 0,
            'high_frequency_volume': 0,
            'high_freq_volume': 0,
            'high_frequency_count': 0,
            'high_freq_ratio': 0,
            'funding_arbitrage_volume': 0,
            'arbitrage_count': 0,
            'funding_arbitrage_ratio': 0,
            'risk_events_count': 0,
            'real_trading_scale': 0
        }