/**
 * 交易详情管理器
 * 负责显示交易列表、筛选、分页、排序等功能
 */

import { formatVolume, formatNumber, showToast, getRiskLevel, getRiskLevelClass, getSeverityText, getDetectionTypeName } from './utils.js';

class TransactionDetailsManager {
    constructor(domManager) {
        this.elements = domManager.elements;
        this.allTransactions = [];
        this.filteredTransactions = [];
        this.currentPage = 1;
        this.pageSize = 10;
        this.sortField = 'risk_score';
        this.sortDirection = 'desc';
        this.currentFilters = {};
        
        this.initializeEventListeners();
    }

    /**
     * 初始化事件监听器
     */
    initializeEventListeners() {
        const elements = this.elements;

        // 风险类型筛选
        if (elements.riskTypeFilter) {
            elements.riskTypeFilter.addEventListener('change', () => {
                this.filterTransactions();
            });
        }

        // 交易搜索
        if (elements.searchTransactions) {
            elements.searchTransactions.addEventListener('input', () => {
                this.filterTransactions();
            });
        }

        // 排序
        if (elements.sortBy) {
            elements.sortBy.addEventListener('change', () => {
                this.sortTransactions();
            });
        }

        // 分页按钮
        if (elements.prevPage) {
            elements.prevPage.addEventListener('click', () => {
                this.goToPreviousPage();
            });
        }

        if (elements.nextPage) {
            elements.nextPage.addEventListener('click', () => {
                this.goToNextPage();
            });
        }
    }

    /**
     * 显示交易详情
     */
    display(transactions) {
        console.log('🔍 TransactionDetailsManager.display() 被调用');
        console.log('🔍 传入的transactions:', transactions);
        console.log('🔍 transactions类型:', typeof transactions);
        console.log('🔍 是否为数组:', Array.isArray(transactions));

        if (!transactions || !Array.isArray(transactions)) {
            console.warn('❌ 交易详情数据为空或格式错误');
            this.showNoDataMessage();
            return;
        }

        console.log('✅ 显示交易详情，数量:', transactions.length);
        this.allTransactions = transactions;
        this.filteredTransactions = [...transactions];
        this.currentPage = 1;

        // 🔧 修复：确保DOM元素存在
        if (!this.elements.transactionsList) {
            console.error('❌ transactionsList DOM元素不存在，尝试重新获取');
            this.elements.transactionsList = document.getElementById('transactionsList');
            if (!this.elements.transactionsList) {
                console.error('❌ 无法找到transactionsList元素，延迟重试');
                setTimeout(() => this.display(transactions), 100);
                return;
            }
        }

        // 🔧 修复：跳过筛选，直接显示所有数据
        console.log('🔧 跳过筛选逻辑，直接显示数据');
        this.displayTransactions();

        // 更新分页信息
        this.updatePaginationInfo();

        // 🔧 修复：延迟应用筛选和排序，避免初始化时被清空
        setTimeout(() => {
            this.applyFiltersAndSort();
        }, 50);
    }

    /**
     * 筛选交易
     */
    filterTransactions() {
        console.log('🔍 filterTransactions() 被调用');
        const filters = this.getCurrentFilters();
        console.log('🔍 当前筛选条件:', filters);
        this.currentFilters = filters;

        console.log('🔍 原始交易数量:', this.allTransactions.length);
        this.filteredTransactions = this.allTransactions.filter(transaction => {
            const matches = this.matchesFilters(transaction, filters);
            if (!matches) {
                console.log('🔍 交易被筛选掉:', transaction.detection_type, transaction);
            }
            return matches;
        });
        console.log('🔍 筛选后交易数量:', this.filteredTransactions.length);

        this.currentPage = 1; // 重置到第一页
        this.sortTransactions();
    }

    /**
     * 获取当前筛选条件
     */
    getCurrentFilters() {
        const filters = {};
        const elements = this.elements;

        // 风险类型筛选
        if (elements.riskTypeFilter) {
            const riskType = elements.riskTypeFilter.value.trim();
            if (riskType) {
                filters.riskType = riskType;
            }
        }

        // 搜索关键词
        if (elements.searchTransactions) {
            const searchTerm = elements.searchTransactions.value.trim();
            if (searchTerm) {
                filters.searchTerm = searchTerm.toLowerCase();
            }
        }

        return filters;
    }

    /**
     * 检查交易是否匹配筛选条件
     */
    matchesFilters(transaction, filters) {
        // 风险类型筛选
        if (filters.riskType) {
            const detectionType = transaction.detection_type || '';
            if (!detectionType.includes(filters.riskType)) {
                return false;
            }
        }

        // 搜索关键词筛选
        if (filters.searchTerm) {
            const searchableFields = [
                transaction.counterparty_id,
                transaction.detection_type,
                transaction.currency,
                transaction.trading_pair
            ].filter(Boolean).map(field => field.toString().toLowerCase());

            const matchesSearch = searchableFields.some(field => 
                field.includes(filters.searchTerm)
            );

            if (!matchesSearch) {
                return false;
            }
        }

        return true;
    }

    /**
     * 排序交易
     */
    sortTransactions() {
        const sortBy = this.elements.sortBy?.value || this.sortField;
        const [field, direction] = sortBy.split('_');
        
        this.sortField = field;
        this.sortDirection = direction || 'desc';

        this.filteredTransactions.sort((a, b) => {
            let aValue = this.getSortValue(a, field);
            let bValue = this.getSortValue(b, field);

            // 处理数字比较
            if (typeof aValue === 'string' && !isNaN(parseFloat(aValue))) {
                aValue = parseFloat(aValue);
                bValue = parseFloat(bValue);
            }

            let comparison = 0;
            if (aValue < bValue) comparison = -1;
            if (aValue > bValue) comparison = 1;

            return this.sortDirection === 'desc' ? -comparison : comparison;
        });

        this.displayTransactions();
        this.updatePaginationInfo();
    }

    /**
     * 获取排序值
     */
    getSortValue(transaction, field) {
        switch (field) {
            case 'risk':
                return transaction.risk_score || 0;
            case 'volume':
                return parseFloat(transaction.volume) || 0;
            case 'time':
                return new Date(transaction.time_range || 0).getTime();
            case 'frequency':
                return transaction.frequency || 0;
            default:
                return transaction[field] || '';
        }
    }

    /**
     * 显示当前页交易
     */
    displayTransactions() {
        console.log('🔍 displayTransactions() 被调用');
        const container = this.elements.transactionsList;
        console.log('🔍 transactionsList容器:', container);

        if (!container) {
            console.warn('❌ 交易列表容器不存在');
            return;
        }

        console.log('🔍 filteredTransactions长度:', this.filteredTransactions.length);
        if (this.filteredTransactions.length === 0) {
            console.warn('❌ 筛选后的交易数量为0，显示无数据消息');
            this.showNoDataMessage();
            return;
        }

        // 计算当前页的交易
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const currentTransactions = this.filteredTransactions.slice(startIndex, endIndex);
        console.log('🔍 当前页交易数量:', currentTransactions.length);

        // 生成交易HTML
        const transactionsHTML = currentTransactions.map((transaction, index) => {
            return this.createTransactionElement(transaction, startIndex + index);
        }).join('');

        console.log('✅ 设置容器HTML，长度:', transactionsHTML.length);
        container.innerHTML = transactionsHTML;
    }

    /**
     * 创建交易元素HTML
     */
    createTransactionElement(transaction, index) {
        const riskLevel = getRiskLevel(transaction.risk_score || 0);
        const riskClass = getRiskLevelClass(transaction.risk_score || 0);
        const consistencyIssues = this.checkDataConsistency(transaction);

        return `
            <div class="transaction-item ${riskClass}" data-index="${index}">
                <div class="transaction-header" onclick="toggleDetails(${index})">
                    <div class="transaction-basic-info">
                        <div class="transaction-id">
                            <strong>交易对手: ${transaction.counterparty_id || 'N/A'}</strong>
                            <span class="detection-type">${getDetectionTypeName(transaction.detection_type)}</span>
                        </div>
                        <div class="transaction-meta">
                            <span class="currency">${transaction.currency || 'N/A'}</span>
                            <span class="trading-pair">${transaction.trading_pair || 'N/A'}</span>
                            <span class="time-range">${this.formatTimeRange(transaction.time_range)}</span>
                        </div>
                    </div>
                    
                    <div class="transaction-metrics">
                        <div class="metric-item">
                            <label>风险评分</label>
                            <span class="risk-score ${riskClass}">${transaction.risk_score || 0}</span>
                        </div>
                        <div class="metric-item">
                            <label>交易量</label>
                            <span class="volume">${formatVolume(transaction.volume || 0)}</span>
                        </div>
                        <div class="metric-item">
                            <label>频次</label>
                            <span class="frequency">${transaction.frequency || 0}</span>
                        </div>
                    </div>
                    
                    <div class="transaction-actions">
                        <button class="btn btn-sm btn-outline-info" onclick="showCounterpartyDetails(${index})">
                            <i class="bi bi-info-circle"></i> 详情
                        </button>
                        <button class="btn btn-sm btn-outline-primary" onclick="exportTransactionDetail(${index})">
                            <i class="bi bi-download"></i> 导出
                        </button>
                        <i class="bi bi-chevron-down toggle-icon"></i>
                    </div>
                </div>
                
                ${consistencyIssues.length > 0 ? this.createConsistencyWarning(consistencyIssues) : ''}
                
                <div class="transaction-details" id="details-${index}" style="display: none;">
                    ${this.createTransactionDetailsContent(transaction, index)}
                </div>
            </div>
        `;
    }

    /**
     * 创建交易详情内容
     */
    createTransactionDetailsContent(transaction, index) {
        return `
            <div class="details-content">
                ${this.createBasicInfoSection(transaction)}
                ${this.createDetailsSection(transaction)}
                ${this.createRiskAssessmentSection(transaction)}
                ${this.createAdvancedAnalysisSection(transaction, index)}
            </div>
        `;
    }

    /**
     * 创建基本信息部分
     */
    createBasicInfoSection(transaction) {
        return `
            <div class="details-section">
                <h4><i class="bi bi-info-circle"></i> 基本信息</h4>
                <div class="info-grid">
                    <div class="info-item">
                        <label>交易对手ID:</label>
                        <span>${transaction.counterparty_id || 'N/A'}</span>
                    </div>
                    <div class="info-item">
                        <label>检测类型:</label>
                                                    <span class="detection-type-badge">${getDetectionTypeName(transaction.detection_type)}</span>
                    </div>
                    <div class="info-item">
                        <label>交易币种:</label>
                        <span>${transaction.currency || 'N/A'}</span>
                    </div>
                    <div class="info-item">
                        <label>交易对:</label>
                        <span>${transaction.trading_pair || 'N/A'}</span>
                    </div>
                    <div class="info-item">
                        <label>时间范围:</label>
                        <span>${this.formatTimeRange(transaction.time_range)}</span>
                    </div>
                    <div class="info-item">
                        <label>数据来源:</label>
                        <span>${transaction.data_source || '系统检测'}</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建详细信息部分
     */
    createDetailsSection(transaction) {
        return `
            <div class="details-section">
                <h4><i class="bi bi-bar-chart"></i> 交易详情</h4>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <label>交易总量</label>
                        <span class="metric-value">${formatVolume(transaction.volume || 0)}</span>
                    </div>
                    <div class="metric-card">
                        <label>交易频次</label>
                        <span class="metric-value">${transaction.frequency || 0}</span>
                    </div>
                    <div class="metric-card">
                        <label>平均单笔</label>
                        <span class="metric-value">${this.calculateAverageAmount(transaction)}</span>
                    </div>
                    <div class="metric-card">
                        <label>风险评分</label>
                        <span class="metric-value risk-score">${transaction.risk_score || 0}</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建风险评估部分
     */
    createRiskAssessmentSection(transaction) {
        const indicators = transaction.risk_indicators || {};
        const indicatorItems = Object.entries(indicators).map(([key, value]) => {
            const percentage = Math.round(value * 100);
            const colorClass = this.getIndicatorColor(percentage);
            const label = this.getIndicatorLabel(key);
            
            return `
                <div class="indicator-item">
                    <div class="indicator-header">
                        <span class="indicator-label">${label}</span>
                        <span class="indicator-value ${colorClass}">${percentage}%</span>
                    </div>
                    <div class="indicator-bar">
                        <div class="indicator-fill ${colorClass}" style="width: ${percentage}%"></div>
                    </div>
                </div>
            `;
        }).join('');

        return `
            <div class="details-section">
                <h4><i class="bi bi-shield-exclamation"></i> 风险评估</h4>
                <div class="risk-indicators">
                    ${indicatorItems || '<p class="no-data">暂无风险指标数据</p>'}
                </div>
                <div class="risk-summary">
                    <div class="risk-level">
                        <label>风险等级:</label>
                        <span class="risk-badge ${getRiskLevelClass(transaction.risk_score)}">${getRiskLevel(transaction.risk_score)}</span>
                    </div>
                    <div class="severity">
                        <label>严重程度:</label>
                        <span class="severity-badge">${getSeverityText(transaction.severity)}</span>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 创建高级分析部分
     */
    createAdvancedAnalysisSection(transaction, index) {
        return `
            <div class="details-section">
                <h4><i class="bi bi-graph-up"></i> 高级分析</h4>
                <div class="analysis-actions">
                    <button class="btn btn-sm btn-outline-secondary" onclick="showTimePatternModal(${index})">
                        <i class="bi bi-clock"></i> 时间模式分析
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="showNetworkVisualization(${index})">
                        <i class="bi bi-diagram-3"></i> 网络关系图
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="analyzeCounterpartyRelation('${transaction.counterparty_id}')">
                        <i class="bi bi-people"></i> 关联分析
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * 创建数据一致性警告
     */
    createConsistencyWarning(issues) {
        const issueItems = issues.map(issue => `<li>${issue}</li>`).join('');
        
        return `
            <div class="consistency-warning">
                <div class="warning-header">
                    <i class="bi bi-exclamation-triangle"></i>
                    <span>数据一致性警告</span>
                </div>
                <ul class="warning-list">
                    ${issueItems}
                </ul>
            </div>
        `;
    }

    /**
     * 检查数据一致性
     */
    checkDataConsistency(transaction) {
        const issues = [];

        // 检查交易量和频次的合理性
        if (transaction.volume && transaction.frequency) {
            const avgAmount = parseFloat(transaction.volume) / transaction.frequency;
            if (avgAmount < 0.01) {
                issues.push('平均单笔交易金额异常低');
            }
            if (avgAmount > 1000000) {
                issues.push('平均单笔交易金额异常高');
            }
        }

        // 检查风险评分的合理性
        if (transaction.risk_score > 100 || transaction.risk_score < 0) {
            issues.push('风险评分超出正常范围');
        }

        // 检查时间范围格式
        if (transaction.time_range && !this.isValidTimeRange(transaction.time_range)) {
            issues.push('时间范围格式异常');
        }

        return issues;
    }

    /**
     * 分页相关方法
     */
    goToPreviousPage() {
        if (this.currentPage > 1) {
            this.currentPage--;
            this.displayTransactions();
            this.updatePaginationInfo();
        }
    }

    goToNextPage() {
        const totalPages = Math.ceil(this.filteredTransactions.length / this.pageSize);
        if (this.currentPage < totalPages) {
            this.currentPage++;
            this.displayTransactions();
            this.updatePaginationInfo();
        }
    }

    updatePaginationInfo() {
        const pageInfo = this.elements.pageInfo;
        if (!pageInfo) return;

        const totalItems = this.filteredTransactions.length;
        const totalPages = Math.ceil(totalItems / this.pageSize);
        const startItem = (this.currentPage - 1) * this.pageSize + 1;
        const endItem = Math.min(this.currentPage * this.pageSize, totalItems);

        pageInfo.textContent = `第 ${startItem}-${endItem} 项，共 ${totalItems} 项 (第 ${this.currentPage}/${totalPages} 页)`;

        // 更新分页按钮状态
        if (this.elements.prevPage) {
            this.elements.prevPage.disabled = this.currentPage === 1;
        }
        if (this.elements.nextPage) {
            this.elements.nextPage.disabled = this.currentPage === totalPages;
        }
    }

    /**
     * 工具方法 - 注释掉前端计算，保留字段映射
     */
    calculateAverageAmount(transaction) {
        // 注释掉前端计算逻辑，直接返回后端数据或占位符
        // if (!transaction.volume || !transaction.frequency) return 'N/A';
        // const avg = parseFloat(transaction.volume) / transaction.frequency;
        // return formatVolume(avg);
        
        // 直接返回后端计算的结果或占位符
        return transaction.average_amount || '等待后端计算';
    }

    formatTimeRange(timeRange) {
        if (!timeRange) return 'N/A';
        return timeRange;
    }

    isValidTimeRange(timeRange) {
        // 简单的时间范围格式验证
        return timeRange && typeof timeRange === 'string' && timeRange.length > 0;
    }



    getIndicatorLabel(key) {
        const labelMap = {
            'volume_anomaly': '交易量异常',
            'frequency_anomaly': '频次异常',
            'time_concentration': '时间集中度',
            'pattern_similarity': '模式相似度',
            'network_centrality': '网络中心性'
        };
        return labelMap[key] || key;
    }

    getIndicatorColor(percentage) {
        if (percentage >= 80) return 'critical';
        if (percentage >= 60) return 'high';
        if (percentage >= 40) return 'medium';
        if (percentage >= 20) return 'low';
        return 'safe';
    }

    /**
     * 显示无数据消息
     */
    showNoDataMessage() {
        const container = this.elements.transactionsList;
        if (container) {
            container.innerHTML = `
                <div class="no-data-message">
                    <i class="bi bi-inbox"></i>
                    <h3>暂无交易数据</h3>
                    <p>当前筛选条件下没有找到相关交易记录</p>
                </div>
            `;
        }
    }

    /**
     * 应用筛选和排序
     */
    applyFiltersAndSort() {
        this.filterTransactions();
    }

    /**
     * 清空显示
     */
    clear() {
        this.allTransactions = [];
        this.filteredTransactions = [];
        this.currentPage = 1;
        this.currentFilters = {};

        if (this.elements.transactionsList) {
            this.elements.transactionsList.innerHTML = '<p class="no-data">暂无数据</p>';
        }

        if (this.elements.pageInfo) {
            this.elements.pageInfo.textContent = '';
        }
    }

    /**
     * 获取当前数据
     */
    getCurrentTransactions() {
        return this.filteredTransactions;
    }

    getAllTransactions() {
        return this.allTransactions;
    }

    getCurrentPage() {
        return this.currentPage;
    }

    getPageSize() {
        return this.pageSize;
    }
}

// 全局函数，保持向后兼容
window.toggleDetails = function(index) {
    const detailsElement = document.getElementById(`details-${index}`);
    const toggleIcon = document.querySelector(`[data-index="${index}"] .toggle-icon`);
    
    if (detailsElement) {
        const isVisible = detailsElement.style.display !== 'none';
        detailsElement.style.display = isVisible ? 'none' : 'block';
        
        if (toggleIcon) {
            toggleIcon.className = isVisible ? 'bi bi-chevron-down toggle-icon' : 'bi bi-chevron-up toggle-icon';
        }
    }
};

window.showCounterpartyDetails = function(index) {
    console.log('显示交易对手详情:', index);

    // 获取交易数据
    const transactionDetailsManager = window.transactionDetailsManager;
    if (!transactionDetailsManager || !transactionDetailsManager.filteredTransactions) {
        console.error('交易详情管理器未初始化');
        return;
    }

    const transaction = transactionDetailsManager.filteredTransactions[index];
    if (!transaction) {
        console.error('未找到交易数据:', index);
        return;
    }

    // 显示对敲交易详情模态框
    showWashTradingDetailsModal(transaction);
};

/**
 * 显示对敲交易详情模态框
 */
function showWashTradingDetailsModal(transaction) {
    // 检查是否是对敲交易
    if (!transaction.detection_type || !transaction.detection_type.includes('wash')) {
        alert('该交易不是对敲交易类型');
        return;
    }

    // 获取对敲交易详情
    fetchWashTradingDetails(transaction)
        .then(details => {
            if (details && details.length > 0) {
                displayWashTradingModal(details, transaction);
            } else {
                displaySimpleTransactionModal(transaction);
            }
        })
        .catch(error => {
            console.error('获取对敲交易详情失败:', error);
            displaySimpleTransactionModal(transaction);
        });
}

/**
 * 获取对敲交易详情
 */
async function fetchWashTradingDetails(transaction) {
    try {
        // 尝试从additional_data中获取详情
        if (transaction.additional_data) {
            let additionalData;
            if (typeof transaction.additional_data === 'string') {
                additionalData = JSON.parse(transaction.additional_data);
            } else {
                additionalData = transaction.additional_data;
            }

            // 检查是否有交易对详情
            if (additionalData.trade_pair_detail) {
                return [additionalData.trade_pair_detail];
            }
        }

        // 如果没有详情，尝试通过API获取
        // 这里需要result_id，暂时返回空数组
        console.log('未找到交易对详情，使用基本信息显示');
        return [];

    } catch (error) {
        console.error('解析交易详情失败:', error);
        return [];
    }
}

/**
 * 显示对敲交易详情模态框
 */
function displayWashTradingModal(tradePairDetails, transaction) {
    const modal = document.getElementById('counterpartyModal');
    if (!modal) {
        console.error('对敲详情模态框不存在');
        return;
    }

    // 构建模态框内容
    const modalContent = createWashTradingModalContent(tradePairDetails, transaction);

    // 更新模态框内容
    const modalBody = modal.querySelector('#counterpartyDetails') || modal.querySelector('.modal-content');
    if (modalBody) {
        modalBody.innerHTML = modalContent;
    }

    // 显示模态框
    modal.style.display = 'block';

    // 添加关闭事件
    const closeBtn = modal.querySelector('.close');
    if (closeBtn) {
        closeBtn.onclick = () => {
            modal.style.display = 'none';
        };
    }

    // 点击模态框外部关闭
    window.onclick = (event) => {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    };
}

/**
 * 创建对敲交易模态框内容
 */
function createWashTradingModalContent(tradePairDetails, transaction) {
    const pairDetail = tradePairDetails[0]; // 取第一个交易对详情

    return `
        <div class="wash-trading-details">
            <h3><i class="bi bi-arrow-left-right"></i> 对敲交易详情</h3>

            <!-- 基本信息 -->
            <div class="basic-info-section">
                <h4><i class="bi bi-info-circle"></i> 基本信息</h4>
                <div class="info-grid">
                    <div class="info-item">
                        <label>合约名称:</label>
                        <span>${transaction.contract_name || 'N/A'}</span>
                    </div>
                    <div class="info-item">
                        <label>检测类型:</label>
                        <span class="detection-type">${transaction.detection_type || 'N/A'}</span>
                    </div>
                    <div class="info-item">
                        <label>风险评分:</label>
                        <span class="risk-score">${transaction.risk_score || 'N/A'}</span>
                    </div>
                    <div class="info-item">
                        <label>总交易量:</label>
                        <span>${window.formatNumber(pairDetail.total_amount || transaction.volume || 0)} USDT</span>
                    </div>
                </div>
            </div>

            <!-- 交易对详情 -->
            <div class="trading-pairs-section">
                <h4><i class="bi bi-people"></i> 交易对信息</h4>
                ${createTradingPairContent(pairDetail)}
            </div>

            <!-- 时间分析 -->
            ${createTimeAnalysisContent(pairDetail)}

            <!-- 利润分析 -->
            ${createProfitAnalysisContent(pairDetail)}
        </div>
    `;
}

/**
 * 创建交易对内容
 */
function createTradingPairContent(pairDetail) {
    if (!pairDetail || !pairDetail.user_a || !pairDetail.user_b) {
        return '<div class="no-data">暂无详细交易对信息</div>';
    }

    const userA = pairDetail.user_a;
    const userB = pairDetail.user_b;

    return `
        <div class="trading-pair-container">
            <div class="user-trading-info">
                <div class="user-section user-a">
                    <h5><i class="bi bi-person"></i> 交易方 A</h5>
                    <div class="trading-details">
                        <div class="detail-item">
                            <label>用户ID:</label>
                            <span class="user-id">${userA.member_id || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <label>仓位ID:</label>
                            <span class="position-id">${userA.position_id || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <label>开仓时间:</label>
                            <span>${window.formatTime(userA.open_time) || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <label>开仓方向:</label>
                            <span class="side">${formatTradingSide(userA.open_side)}</span>
                        </div>
                        <div class="detail-item">
                            <label>开仓金额:</label>
                            <span class="amount">${window.formatNumber(userA.open_amount || 0)} USDT</span>
                        </div>
                        <div class="detail-item">
                            <label>平仓时间:</label>
                            <span>${window.formatTime(userA.close_time) || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <label>平仓金额:</label>
                            <span class="amount">${window.formatNumber(userA.close_amount || 0)} USDT</span>
                        </div>
                        <div class="detail-item">
                            <label>利润:</label>
                            <span class="profit ${(userA.profit || 0) >= 0 ? 'positive' : 'negative'}">
                                ${window.formatNumber(userA.profit || 0)} USDT
                            </span>
                        </div>
                    </div>
                </div>

                <div class="vs-divider">
                    <i class="bi bi-arrow-left-right"></i>
                    <span>VS</span>
                </div>

                <div class="user-section user-b">
                    <h5><i class="bi bi-person"></i> 交易方 B</h5>
                    <div class="trading-details">
                        <div class="detail-item">
                            <label>用户ID:</label>
                            <span class="user-id">${userB.member_id || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <label>仓位ID:</label>
                            <span class="position-id">${userB.position_id || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <label>开仓时间:</label>
                            <span>${window.formatTime(userB.open_time) || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <label>开仓方向:</label>
                            <span class="side">${formatTradingSide(userB.open_side)}</span>
                        </div>
                        <div class="detail-item">
                            <label>开仓金额:</label>
                            <span class="amount">${window.formatNumber(userB.open_amount || 0)} USDT</span>
                        </div>
                        <div class="detail-item">
                            <label>平仓时间:</label>
                            <span>${window.formatTime(userB.close_time) || 'N/A'}</span>
                        </div>
                        <div class="detail-item">
                            <label>平仓金额:</label>
                            <span class="amount">${window.formatNumber(userB.close_amount || 0)} USDT</span>
                        </div>
                        <div class="detail-item">
                            <label>利润:</label>
                            <span class="profit ${(userB.profit || 0) >= 0 ? 'positive' : 'negative'}">
                                ${window.formatNumber(userB.profit || 0)} USDT
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * 创建时间分析内容
 */
function createTimeAnalysisContent(pairDetail) {
    if (!pairDetail.time_gaps) {
        return '';
    }

    const timeGaps = pairDetail.time_gaps;

    return `
        <div class="time-analysis-section">
            <h4><i class="bi bi-clock"></i> 时间分析</h4>
            <div class="time-metrics">
                <div class="metric-item">
                    <label>开仓时间差:</label>
                    <span class="time-gap">${formatTimeGap(timeGaps.open_gap_seconds)}</span>
                </div>
                <div class="metric-item">
                    <label>平仓时间差:</label>
                    <span class="time-gap">${formatTimeGap(timeGaps.close_gap_seconds)}</span>
                </div>
            </div>
        </div>
    `;
}

/**
 * 创建利润分析内容
 */
function createProfitAnalysisContent(pairDetail) {
    const netProfit = pairDetail.net_profit || 0;
    const totalAmount = pairDetail.total_amount || 0;

    return `
        <div class="profit-analysis-section">
            <h4><i class="bi bi-graph-up"></i> 利润分析</h4>
            <div class="profit-metrics">
                <div class="metric-item">
                    <label>净利润:</label>
                    <span class="net-profit ${netProfit >= 0 ? 'positive' : 'negative'}">
                        ${window.formatNumber(netProfit)} USDT
                    </span>
                </div>
                <div class="metric-item">
                    <label>总交易金额:</label>
                    <span class="total-amount">${window.formatNumber(totalAmount)} USDT</span>
                </div>
                <div class="metric-item">
                    <label>利润率:</label>
                    <span class="profit-rate ${netProfit >= 0 ? 'positive' : 'negative'}">
                        ${totalAmount > 0 ? ((netProfit / totalAmount) * 100).toFixed(2) : '0.00'}%
                    </span>
                </div>
            </div>
        </div>
    `;
}

/**
 * 显示简单交易模态框（当没有详细对敲信息时）
 */
function displaySimpleTransactionModal(transaction) {
    const modal = document.getElementById('counterpartyModal');
    if (!modal) return;

    const modalContent = `
        <div class="simple-transaction-details">
            <h3><i class="bi bi-info-circle"></i> 交易详情</h3>
            <div class="basic-info-section">
                <div class="info-grid">
                    <div class="info-item">
                        <label>交易对手:</label>
                        <span>${transaction.counterparty_id || 'N/A'}</span>
                    </div>
                    <div class="info-item">
                        <label>合约名称:</label>
                        <span>${transaction.contract_name || 'N/A'}</span>
                    </div>
                    <div class="info-item">
                        <label>检测类型:</label>
                        <span>${transaction.detection_type || 'N/A'}</span>
                    </div>
                    <div class="info-item">
                        <label>风险评分:</label>
                        <span class="risk-score">${transaction.risk_score || 'N/A'}</span>
                    </div>
                    <div class="info-item">
                        <label>交易量:</label>
                        <span>${window.formatNumber(transaction.volume || 0)} USDT</span>
                    </div>
                    <div class="info-item">
                        <label>交易频次:</label>
                        <span>${transaction.frequency || 'N/A'}</span>
                    </div>
                </div>
            </div>
            <div class="note">
                <i class="bi bi-info-circle"></i>
                <span>暂无详细的交易对信息</span>
            </div>
        </div>
    `;

    const modalBody = modal.querySelector('#counterpartyDetails') || modal.querySelector('.modal-content');
    if (modalBody) {
        modalBody.innerHTML = modalContent;
    }

    modal.style.display = 'block';
}

/**
 * 格式化交易方向
 */
function formatTradingSide(side) {
    const sideMap = {
        1: '开多',
        2: '平空',
        3: '开空',
        4: '平多'
    };
    return sideMap[side] || `未知(${side})`;
}

/**
 * 格式化时间差
 */
function formatTimeGap(seconds) {
    if (!seconds && seconds !== 0) return 'N/A';

    const absSeconds = Math.abs(seconds);
    if (absSeconds < 60) {
        return `${seconds}秒`;
    } else if (absSeconds < 3600) {
        const minutes = Math.floor(absSeconds / 60);
        const remainingSeconds = absSeconds % 60;
        return `${minutes}分${remainingSeconds}秒`;
    } else {
        const hours = Math.floor(absSeconds / 3600);
        const minutes = Math.floor((absSeconds % 3600) / 60);
        return `${hours}小时${minutes}分`;
    }
}

export default TransactionDetailsManager;