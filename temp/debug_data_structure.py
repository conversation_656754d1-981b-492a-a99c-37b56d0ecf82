#!/usr/bin/env python3
"""
调试前端数据结构问题
"""
import requests
import json

def debug_data_structure():
    """调试数据结构问题"""
    print("🔍 调试前端数据结构问题")
    print("=" * 50)
    
    test_digital_id = "23452801"
    
    try:
        # 登录
        print("1. 登录...")
        login_url = "http://localhost:5005/api/auth/login"
        login_data = {"username": "admin", "password": "admin123"}
        response = requests.post(login_url, json=login_data)
        cookies = response.cookies
        print("   ✅ 登录成功")
        
        # 获取任务
        print("2. 获取任务...")
        tasks_url = "http://localhost:5005/api/user-analysis/available-tasks"
        response = requests.get(tasks_url, cookies=cookies)
        tasks_data = response.json()
        agent_task_id = tasks_data['agent_tasks'][0]['task_id']
        print(f"   ✅ 任务ID: {agent_task_id}")
        
        # 测试两个API端点
        print("3. 测试API端点...")
        
        # API 1: search-by-digital (我们修复的)
        print("   3.1 测试 search-by-digital API:")
        analysis_url = f"http://localhost:5005/api/user-analysis/search-by-digital/{test_digital_id}"
        params = {'agent_task_id': agent_task_id, 'include_behavior': 'false'}
        response = requests.get(analysis_url, params=params, cookies=cookies)
        
        if response.status_code == 200:
            digital_data = response.json()
            print(f"     ✅ 状态码: {response.status_code}")
            print(f"     数据结构:")
            print(f"       - member_id: {'存在' if 'member_id' in digital_data else '不存在'}")
            print(f"       - user_profile: {'存在' if 'user_profile' in digital_data else '不存在'}")
            print(f"       - associations: {'存在' if 'associations' in digital_data else '不存在'}")
            print(f"       - complete_data: {'存在' if 'complete_data' in digital_data else '不存在'}")
            
            if 'associations' in digital_data:
                associations = digital_data['associations']
                print(f"       associations内容:")
                print(f"         - same_ip_count: {associations.get('same_ip_count', 'N/A')}")
                print(f"         - same_device_count: {associations.get('same_device_count', 'N/A')}")
                print(f"         - both_shared_count: {associations.get('both_shared_count', 'N/A')}")
        else:
            print(f"     ❌ 状态码: {response.status_code}")
        
        # API 2: complete-analysis (前端可能调用的)
        print("   3.2 测试 complete-analysis API:")
        member_id = digital_data.get('member_id') if response.status_code == 200 else None
        
        if member_id:
            complete_url = f"http://localhost:5005/api/user-behavior/complete-analysis/{member_id}"
            params = {'task_id': agent_task_id}
            response = requests.get(complete_url, params=params, cookies=cookies)
            
            print(f"     状态码: {response.status_code}")
            if response.status_code == 200:
                complete_data = response.json()
                print(f"     数据结构:")
                print(f"       - complete_data: {'存在' if 'complete_data' in complete_data else '不存在'}")
                
                if 'complete_data' in complete_data:
                    cd = complete_data['complete_data']
                    print(f"       complete_data内容:")
                    print(f"         - user_profile: {'存在' if 'user_profile' in cd else '不存在'}")
                    print(f"         - associations: {'存在' if 'associations' in cd else '不存在'}")
                    
                    if 'associations' in cd:
                        associations = cd['associations']
                        print(f"         associations内容:")
                        print(f"           - same_ip_count: {associations.get('same_ip_count', 'N/A')}")
                        print(f"           - same_device_count: {associations.get('same_device_count', 'N/A')}")
                        print(f"           - both_shared_count: {associations.get('both_shared_count', 'N/A')}")
                    else:
                        print(f"         ❌ complete_data中没有associations")
            else:
                print(f"     ❌ complete-analysis API失败: {response.text}")
        else:
            print(f"     ❌ 无法获取member_id，跳过complete-analysis测试")
        
        # 保存调试数据
        debug_info = {
            "digital_api": {
                "url": analysis_url,
                "status": response.status_code if 'response' in locals() else 'N/A',
                "has_associations": 'associations' in digital_data if response.status_code == 200 else False,
                "data": digital_data if response.status_code == 200 else None
            },
            "complete_api": {
                "url": complete_url if member_id else 'N/A',
                "status": response.status_code if member_id else 'N/A',
                "has_complete_data": 'complete_data' in complete_data if member_id and response.status_code == 200 else False,
                "has_associations_in_complete": 'associations' in complete_data.get('complete_data', {}) if member_id and response.status_code == 200 else False
            }
        }
        
        with open('debug_data_structure.json', 'w', encoding='utf-8') as f:
            json.dump(debug_info, f, ensure_ascii=False, indent=2)
        
        print(f"\n   ✅ 调试信息已保存到 debug_data_structure.json")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_frontend_flow():
    """分析前端数据流"""
    print(f"\n📊 分析前端数据流")
    print("=" * 50)
    
    print("前端数据流分析:")
    print("1. 用户输入digital_id: 23452801")
    print("2. SearchManager.handleSearch() 被调用")
    print("3. 检测到digital_id类型")
    print("4. 调用 userAnalysisService.searchUserAnalysisByDigitalId()")
    print("   - 这会调用 /api/user-analysis/search-by-digital/23452801")
    print("   - 返回包含 associations 的数据")
    print("5. 然后调用 userBehaviorService.getUserCompleteAnalysis()")
    print("   - 这会调用 /api/user-behavior/complete-analysis/{member_id}")
    print("   - 可能不包含 associations 数据")
    print("6. 最终调用 displayCompleteUserAnalysis()")
    print("   - 期望 completeData.complete_data.associations")
    print("   - 但实际可能没有这个数据")
    
    print(f"\n🔧 问题分析:")
    print("1. search-by-digital API 返回正确的 associations 数据")
    print("2. 但前端最终使用的是 complete-analysis API 的数据")
    print("3. complete-analysis API 可能没有包含 associations 数据")
    print("4. 导致 displayAssociationsComplete() 接收到空数据")
    
    print(f"\n💡 解决方案:")
    print("1. 修改 complete-analysis API，确保包含 associations 数据")
    print("2. 或者修改前端逻辑，合并两个API的数据")
    print("3. 或者让前端直接使用 search-by-digital API 的数据")

def generate_fix_solution():
    """生成修复方案"""
    print(f"\n🛠️ 生成修复方案")
    print("=" * 50)
    
    # 方案1: 修改后端complete-analysis API
    print("方案1: 修改后端 complete-analysis API")
    print("- 在 user-behavior API 中添加关联分析数据")
    print("- 确保返回的 complete_data 包含 associations")
    print("- 优点: 前端无需修改")
    print("- 缺点: 需要修改后端逻辑")
    
    print(f"\n方案2: 修改前端数据合并逻辑")
    print("- 在 SearchManager 中合并两个API的数据")
    print("- 将 search-by-digital 的 associations 数据合并到 complete-analysis 结果中")
    print("- 优点: 后端无需大改")
    print("- 缺点: 前端逻辑复杂")
    
    print(f"\n方案3: 前端直接使用 search-by-digital 数据")
    print("- 修改前端，直接使用 search-by-digital API 返回的数据")
    print("- 不再调用 complete-analysis API")
    print("- 优点: 简单直接")
    print("- 缺点: 可能缺少其他数据")
    
    print(f"\n🎯 推荐方案: 方案2 (前端数据合并)")
    print("理由:")
    print("- search-by-digital API 已经返回正确的关联分析数据")
    print("- 只需要在前端合并数据，不影响其他功能")
    print("- 修改范围小，风险低")

if __name__ == "__main__":
    print("🐛 前端数据结构调试")
    print("=" * 60)
    
    # 调试数据结构
    success = debug_data_structure()
    
    # 分析前端流程
    analyze_frontend_flow()
    
    # 生成修复方案
    generate_fix_solution()
    
    print(f"\n📋 调试结果:")
    print(f"✅ 数据结构分析: {'完成' if success else '失败'}")
    print(f"✅ 问题定位: 前端数据流问题")
    print(f"✅ 修复方案: 已提供")
    
    print(f"\n🎯 下一步行动:")
    print(f"1. 查看 debug_data_structure.json 了解详细数据结构")
    print(f"2. 选择合适的修复方案")
    print(f"3. 实施修复并测试")
