#!/usr/bin/env python3
"""
调试前端数据接收问题
"""
import requests
import json

def debug_frontend_data_flow():
    """调试前端数据流"""
    print("🔍 调试前端数据接收问题")
    print("=" * 50)
    
    test_digital_id = "23452801"
    
    try:
        # 登录
        print("1. 登录...")
        login_url = "http://localhost:5005/api/auth/login"
        login_data = {"username": "admin", "password": "admin123"}
        response = requests.post(login_url, json=login_data)
        cookies = response.cookies
        print("   ✅ 登录成功")
        
        # 获取任务
        print("2. 获取任务...")
        tasks_url = "http://localhost:5005/api/user-analysis/available-tasks"
        response = requests.get(tasks_url, cookies=cookies)
        tasks_data = response.json()
        agent_task_id = tasks_data['agent_tasks'][0]['task_id']
        print(f"   ✅ 任务ID: {agent_task_id}")
        
        # 测试API调用
        print("3. 调用API...")
        analysis_url = f"http://localhost:5005/api/user-analysis/search-by-digital/{test_digital_id}"
        params = {'agent_task_id': agent_task_id, 'include_behavior': 'false'}
        response = requests.get(analysis_url, params=params, cookies=cookies)
        
        print(f"   状态码: {response.status_code}")
        print(f"   URL: {analysis_url}")
        print(f"   参数: {params}")
        
        if response.status_code == 200:
            analysis_data = response.json()
            
            print("4. 分析API响应数据...")
            
            # 检查associations字段
            associations = analysis_data.get('associations', {})
            print(f"   associations字段存在: {'是' if associations else '否'}")
            
            if associations:
                print(f"   associations内容:")
                for key, value in associations.items():
                    if key.endswith('_count'):
                        print(f"     {key}: {value}")
                    elif key.endswith('_users'):
                        print(f"     {key}: {len(value) if isinstance(value, list) else 'N/A'} 个用户")
                
                # 详细检查用户数据
                print(f"\n   详细用户数据:")
                for list_name in ['same_ip_users', 'same_device_users', 'both_shared_users']:
                    users = associations.get(list_name, [])
                    print(f"     {list_name}: {len(users)} 个")
                    if users:
                        for i, user in enumerate(users[:2]):  # 显示前2个
                            print(f"       用户{i+1}: {user.get('member_id', 'N/A')}")
                            print(f"         digital_id: {user.get('digital_id', 'N/A')}")
                            print(f"         shared_ip: {user.get('shared_ip', 'N/A')}")
                            print(f"         shared_device: {user.get('shared_device', 'N/A')[:16] if user.get('shared_device') else 'N/A'}...")
                            print(f"         bd_name: {user.get('bd_name', 'N/A')}")
            
            # 保存调试数据
            debug_data = {
                "api_url": analysis_url,
                "params": params,
                "status_code": response.status_code,
                "response_data": analysis_data,
                "associations_summary": {
                    "same_ip_count": associations.get('same_ip_count', 0),
                    "same_device_count": associations.get('same_device_count', 0),
                    "both_shared_count": associations.get('both_shared_count', 0),
                    "same_ip_users_length": len(associations.get('same_ip_users', [])),
                    "same_device_users_length": len(associations.get('same_device_users', [])),
                    "both_shared_users_length": len(associations.get('both_shared_users', []))
                }
            }
            
            with open('debug_frontend_data.json', 'w', encoding='utf-8') as f:
                json.dump(debug_data, f, ensure_ascii=False, indent=2)
            
            print(f"\n   ✅ 调试数据已保存到 debug_frontend_data.json")
            
            return True
        else:
            print(f"   ❌ API调用失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def generate_frontend_debug_code():
    """生成前端调试代码"""
    print(f"\n📋 生成前端调试代码")
    print("=" * 50)
    
    debug_code = """
// 前端调试代码 - 在浏览器控制台中运行

// 1. 检查当前页面的数据接收
function debugCurrentPageData() {
    console.log('=== 前端数据调试 ===');
    
    // 检查全局变量
    if (typeof window.userAnalysisManager !== 'undefined') {
        console.log('✅ userAnalysisManager 存在');
    } else {
        console.log('❌ userAnalysisManager 不存在');
    }
    
    // 检查最近的API响应
    if (window.lastApiResponse) {
        console.log('✅ 最近的API响应:', window.lastApiResponse);
        
        const associations = window.lastApiResponse.associations;
        if (associations) {
            console.log('关联分析数据:');
            console.log('- same_ip_count:', associations.same_ip_count);
            console.log('- same_device_count:', associations.same_device_count);
            console.log('- both_shared_count:', associations.both_shared_count);
            console.log('- same_ip_users length:', associations.same_ip_users?.length || 0);
            console.log('- same_device_users length:', associations.same_device_users?.length || 0);
            console.log('- both_shared_users length:', associations.both_shared_users?.length || 0);
        } else {
            console.log('❌ 没有关联分析数据');
        }
    } else {
        console.log('❌ 没有最近的API响应数据');
    }
}

// 2. 手动调用显示函数
function testDisplayAssociations() {
    console.log('=== 测试显示函数 ===');
    
    // 模拟测试数据
    const testData = {
        same_ip_count: 2,
        same_device_count: 1,
        both_shared_count: 1,
        same_ip_users: [
            {
                member_id: '2198b7c8d4384fe9820a5dd5d5721c4b',
                digital_id: '49428394',
                shared_ip: '**************',
                bd_name: 'Ourbit Official'
            }
        ],
        same_device_users: [
            {
                member_id: '2198b7c8d4384fe9820a5dd5d5721c4b',
                digital_id: '49428394',
                shared_device: '86bec2285690ee9c...',
                bd_name: 'Ourbit Official'
            }
        ],
        both_shared_users: [
            {
                member_id: '2198b7c8d4384fe9820a5dd5d5721c4b',
                digital_id: '49428394',
                shared_ip: '**************',
                shared_device: '86bec2285690ee9c...',
                bd_name: 'Ourbit Official'
            }
        ]
    };
    
    // 检查显示函数是否存在
    if (typeof window.userAnalysisManager !== 'undefined' && 
        typeof window.userAnalysisManager.displayAssociationsComplete === 'function') {
        console.log('✅ displayAssociationsComplete 函数存在');
        
        // 手动调用显示函数
        try {
            window.userAnalysisManager.displayAssociationsComplete(testData);
            console.log('✅ 手动调用显示函数成功');
        } catch (error) {
            console.error('❌ 手动调用显示函数失败:', error);
        }
    } else {
        console.log('❌ displayAssociationsComplete 函数不存在');
    }
}

// 3. 检查DOM元素
function checkDOMElements() {
    console.log('=== 检查DOM元素 ===');
    
    const elements = [
        'sameIpCount',
        'sameDeviceCount', 
        'bothSharedCount',
        'sameIpUsers',
        'sameDeviceUsers',
        'bothSharedUsers'
    ];
    
    elements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            console.log(`✅ ${id}: 存在, 内容: "${element.textContent || element.innerHTML}"`);
        } else {
            console.log(`❌ ${id}: 不存在`);
        }
    });
}

// 4. 监听网络请求
function monitorNetworkRequests() {
    console.log('=== 监听网络请求 ===');
    
    // 重写fetch函数来监听请求
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        console.log('🌐 Fetch请求:', args[0]);
        return originalFetch.apply(this, args).then(response => {
            if (args[0].includes('user-analysis')) {
                console.log('📥 用户分析API响应:', response.status);
                response.clone().json().then(data => {
                    console.log('📊 响应数据:', data);
                    window.lastApiResponse = data;
                }).catch(e => console.log('解析响应数据失败:', e));
            }
            return response;
        });
    };
    
    console.log('✅ 网络请求监听已启用');
}

// 运行所有调试函数
console.log('🔍 开始前端调试...');
debugCurrentPageData();
checkDOMElements();
testDisplayAssociations();
monitorNetworkRequests();
console.log('✅ 前端调试完成');
"""
    
    with open('frontend_debug_code.js', 'w', encoding='utf-8') as f:
        f.write(debug_code)
    
    print("   ✅ 前端调试代码已保存到 frontend_debug_code.js")
    print("   📝 使用方法:")
    print("   1. 打开个人分析页面")
    print("   2. 按F12打开开发者工具")
    print("   3. 复制 frontend_debug_code.js 中的代码到控制台")
    print("   4. 执行代码查看调试信息")
    print("   5. 然后输入测试用户ID并搜索")
    print("   6. 观察控制台输出的调试信息")

def provide_solution_steps():
    """提供解决方案步骤"""
    print(f"\n🛠️ 解决方案步骤")
    print("=" * 50)
    
    steps = [
        {
            "步骤": "1. 确认API数据正确",
            "操作": [
                "运行 python3 debug_frontend_data.py",
                "检查 debug_frontend_data.json 中的数据",
                "确认 associations 字段包含正确的用户数据"
            ]
        },
        {
            "步骤": "2. 检查前端接收",
            "操作": [
                "在浏览器中运行前端调试代码",
                "检查 window.lastApiResponse 是否包含数据",
                "确认 displayAssociationsComplete 函数被调用"
            ]
        },
        {
            "步骤": "3. 验证DOM更新",
            "操作": [
                "检查页面元素 ID 是否正确",
                "确认 updateElementText 函数正常工作",
                "验证 displayAssociatedUsers 函数被调用"
            ]
        },
        {
            "步骤": "4. 手动测试显示",
            "操作": [
                "使用测试数据手动调用显示函数",
                "检查页面是否正确更新",
                "确认用户列表是否显示"
            ]
        }
    ]
    
    for step in steps:
        print(f"\n{step['步骤']}:")
        for operation in step['操作']:
            print(f"   • {operation}")

if __name__ == "__main__":
    print("🐛 前端数据接收调试")
    print("=" * 60)
    
    # 调试API数据
    api_success = debug_frontend_data_flow()
    
    # 生成前端调试代码
    generate_frontend_debug_code()
    
    # 提供解决方案
    provide_solution_steps()
    
    print(f"\n📋 调试清单:")
    print(f"✅ API数据: {'正常' if api_success else '异常'}")
    print(f"✅ 调试工具: 已生成")
    print(f"✅ 解决方案: 已提供")
    
    print(f"\n🎯 立即行动:")
    print(f"1. 在浏览器中打开个人分析页面")
    print(f"2. 运行前端调试代码")
    print(f"3. 输入测试用户ID: 23452801")
    print(f"4. 观察控制台输出，找出问题所在")
