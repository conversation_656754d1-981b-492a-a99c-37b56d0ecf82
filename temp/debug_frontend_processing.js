/**
 * 调试前端数据处理逻辑
 */

// 模拟从API获取的数据
async function loadApiResponse() {
    try {
        const response = await fetch('/temp/api_response_debug.json');
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('加载API响应失败:', error);
        return null;
    }
}

// 模拟前端处理逻辑
function processTransactionDetails(userData) {
    console.log('=== 开始处理交易详情 ===');
    
    if (!userData.transaction_details) {
        console.error('❌ 没有transaction_details数据');
        return;
    }
    
    console.log('✅ 找到transaction_details');
    console.log('transaction_details结构:', Object.keys(userData.transaction_details));
    
    // 检查risk_transactions字段
    const riskTransactions = userData.transaction_details.risk_transactions || [];
    console.log(`✅ risk_transactions数量: ${riskTransactions.length}`);
    
    if (riskTransactions.length > 0) {
        console.log('前3条风险交易:');
        riskTransactions.slice(0, 3).forEach((tx, index) => {
            console.log(`  交易${index + 1}:`, {
                detection_type: tx.detection_type,
                risk_score: tx.risk_score,
                volume: tx.volume || tx.abnormal_volume,
                timestamp: tx.timestamp || tx.created_at
            });
        });
        
        // 模拟前端显示逻辑
        displayRiskTransactionsList(riskTransactions);
    } else {
        console.warn('❌ 没有风险交易数据');
        showNoDataMessage();
    }
}

// 模拟前端显示函数
function displayRiskTransactionsList(transactions) {
    console.log('\n=== 模拟前端显示逻辑 ===');
    
    if (!transactions.length) {
        console.log('显示: 暂无风险交易记录');
        return;
    }
    
    console.log(`准备显示 ${transactions.length} 条交易记录`);
    
    // 模拟生成HTML
    const htmlItems = transactions.slice(0, 5).map((tx, index) => {
        return {
            index: index,
            member_id: tx.member_id || tx.transaction_id || 'N/A',
            risk_type: formatRiskType(tx.detection_type || tx.risk_type),
            risk_score: tx.risk_score_display || tx.risk_score || '--',
            volume: formatNumber(tx.volume || tx.transaction_volume || tx.abnormal_volume || 0),
            detection_method: tx.detection_type || tx.detection_method || 'N/A',
            timestamp: formatTime(tx.timestamp || tx.transaction_time || tx.created_at)
        };
    });
    
    console.log('生成的HTML数据项:');
    htmlItems.forEach((item, index) => {
        console.log(`  项目${index + 1}:`, item);
    });
}

function showNoDataMessage() {
    console.log('显示: 暂无风险交易记录');
}

// 辅助函数
function formatRiskType(type) {
    const typeMap = {
        'wash_trading': '对敲交易',
        'high_frequency': '高频交易',
        'arbitrage': '套利交易'
    };
    return typeMap[type] || type || '未知';
}

function formatNumber(num) {
    if (!num) return '0';
    return parseFloat(num).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}

function formatTime(timeStr) {
    if (!timeStr) return 'N/A';
    try {
        const date = new Date(timeStr);
        return date.toLocaleString('zh-CN');
    } catch (error) {
        return timeStr;
    }
}

// 主测试函数
async function testFrontendProcessing() {
    console.log('🚀 开始测试前端数据处理逻辑');
    
    const userData = await loadApiResponse();
    if (!userData) {
        console.error('❌ 无法加载测试数据');
        return;
    }
    
    console.log('✅ 成功加载API响应数据');
    console.log('用户ID:', userData.member_id);
    console.log('响应状态:', userData.status);
    
    // 处理交易详情
    processTransactionDetails(userData);
}

// 页面加载后执行测试
if (typeof window !== 'undefined') {
    window.addEventListener('DOMContentLoaded', testFrontendProcessing);
} else {
    // Node.js环境
    testFrontendProcessing();
}
