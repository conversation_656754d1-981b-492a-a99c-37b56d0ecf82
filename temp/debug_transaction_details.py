#!/usr/bin/env python3
"""
调试异常交易详情显示问题
"""

import requests
import json
import duckdb

def test_user_analysis_api():
    """测试用户分析API返回的交易详情"""
    
    user_id = '9c8db566f6354095bf237e962d0ab4c5'
    
    print(f"=== 测试用户 {user_id} 的分析API ===")
    
    # 1. 直接查询数据库
    print("\n1. 直接查询数据库中的异常交易记录:")
    conn = duckdb.connect('data/risk_analysis.duckdb')
    
    sql = """
    SELECT crd.member_id, crd.detection_type, crd.risk_score, 
           crd.abnormal_volume, crd.trade_count, crd.additional_data,
           ar.task_id, ar.algorithm_type
    FROM contract_risk_details crd
    JOIN algorithm_results ar ON crd.algorithm_result_id = ar.id
    WHERE crd.member_id = ?
    ORDER BY crd.created_at DESC
    LIMIT 10
    """
    
    results = conn.execute(sql, [user_id]).fetchall()
    print(f"数据库中找到 {len(results)} 条记录:")
    for i, row in enumerate(results[:3]):  # 只显示前3条
        print(f"  记录{i+1}: {row[1]}, 风险评分={row[2]}, 交易量={row[3]}")
        if row[5]:  # additional_data
            try:
                additional = json.loads(row[5]) if isinstance(row[5], str) else row[5]
                print(f"    附加数据: {list(additional.keys()) if isinstance(additional, dict) else 'N/A'}")
            except:
                print(f"    附加数据解析失败")

    # 关闭数据库连接
    conn.close()

    # 2. 测试后端API
    print(f"\n2. 测试后端API:")
    
    # 先登录
    login_url = "http://localhost:5005/api/auth/login"
    login_data = {"username": "admin", "password": "admin123"}

    session = requests.Session()
    login_response = session.post(login_url, json=login_data)
    
    if login_response.status_code == 200:
        print("✅ 登录成功")
        
        # 调用用户分析API
        api_url = f"http://localhost:5005/api/user-analysis/search/{user_id}"
        
        response = session.get(api_url)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API调用成功")
            
            # 检查交易详情
            transaction_details = data.get('transaction_details', {})
            print(f"\n交易详情结构:")
            print(f"  - total_transactions: {transaction_details.get('total_transactions', 'N/A')}")
            print(f"  - total_risk_transactions: {transaction_details.get('total_risk_transactions', 'N/A')}")
            
            risk_transactions = transaction_details.get('risk_transactions', [])
            print(f"  - risk_transactions数量: {len(risk_transactions)}")
            
            if risk_transactions:
                print(f"\n前3条风险交易:")
                for i, tx in enumerate(risk_transactions[:3]):
                    print(f"    交易{i+1}: {tx.get('detection_type', 'N/A')}, 风险评分={tx.get('risk_score', 'N/A')}")
                    print(f"             交易量={tx.get('volume', 'N/A')}, 时间={tx.get('timestamp', 'N/A')}")
            else:
                print("  ❌ 没有风险交易数据!")
                
                # 检查原始交易数据
                transactions = transaction_details.get('transactions', [])
                print(f"  - 原始transactions数量: {len(transactions)}")
                
                if transactions:
                    print(f"  前3条原始交易:")
                    for i, tx in enumerate(transactions[:3]):
                        print(f"    交易{i+1}: {tx.get('detection_type', 'N/A')}, 风险评分={tx.get('risk_score', 'N/A')}")
            
            # 保存完整响应到文件
            with open('temp/api_response_debug.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"\n完整API响应已保存到 temp/api_response_debug.json")
            
        else:
            print(f"❌ API调用失败: {response.status_code}")
            print(f"响应: {response.text}")
    else:
        print(f"❌ 登录失败: {login_response.status_code}")
        print(f"响应: {login_response.text}")

if __name__ == "__main__":
    test_user_analysis_api()
