// 修复前端关联分析显示的代码
// 在浏览器控制台中运行此代码

console.log('🔧 开始修复前端关联分析显示...');

// 1. 检查当前状态
function checkCurrentState() {
    console.log('=== 检查当前状态 ===');
    
    // 检查DOM元素
    const elements = [
        'sameIpCount', 'sameDeviceCount', 'bothSharedCount',
        'sameIpUsers', 'sameDeviceUsers', 'bothSharedUsers'
    ];
    
    elements.forEach(id => {
        const element = document.getElementById(id);
        console.log(`${id}: ${element ? '存在' : '不存在'}`);
        if (element) {
            console.log(`  内容: "${element.textContent || element.innerHTML}"`);
        }
    });
    
    // 检查全局变量
    console.log('userAnalysisApp:', typeof window.userAnalysisApp);
    console.log('userAnalysisService:', typeof window.userAnalysisService);
}

// 2. 手动调用API获取数据
async function manualAPICall() {
    console.log('=== 手动API调用 ===');
    
    const testDigitalId = '23452801';
    const agentTaskId = '48fa563f-94cc-4b1b-b746-f7031ace7098';
    
    try {
        const url = `/api/user-analysis/search-by-digital/${testDigitalId}?agent_task_id=${agentTaskId}&include_behavior=false`;
        console.log('请求URL:', url);
        
        const response = await fetch(url, {
            credentials: 'include'
        });
        
        console.log('响应状态:', response.status);
        
        if (response.ok) {
            const data = await response.json();
            console.log('API响应数据:', data);
            
            // 保存到全局变量
            window.debugApiData = data;
            
            // 检查关联分析数据
            const associations = data.associations;
            if (associations) {
                console.log('关联分析数据:');
                console.log('- same_ip_count:', associations.same_ip_count);
                console.log('- same_device_count:', associations.same_device_count);
                console.log('- both_shared_count:', associations.both_shared_count);
                console.log('- same_ip_users:', associations.same_ip_users?.length || 0);
                console.log('- same_device_users:', associations.same_device_users?.length || 0);
                console.log('- both_shared_users:', associations.both_shared_users?.length || 0);
                
                return data;
            } else {
                console.error('❌ 没有关联分析数据');
                return null;
            }
        } else {
            console.error('❌ API请求失败:', response.status, response.statusText);
            return null;
        }
    } catch (error) {
        console.error('❌ API调用异常:', error);
        return null;
    }
}

// 3. 手动更新显示
function manualUpdateDisplay(data) {
    console.log('=== 手动更新显示 ===');
    
    if (!data || !data.associations) {
        console.error('❌ 没有有效的关联分析数据');
        return false;
    }
    
    const associations = data.associations;
    
    try {
        // 更新统计数字
        const updateElement = (id, value) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
                console.log(`✅ 更新 ${id}: ${value}`);
                return true;
            } else {
                console.error(`❌ 元素 ${id} 不存在`);
                return false;
            }
        };
        
        updateElement('sameIpCount', associations.same_ip_count || 0);
        updateElement('sameDeviceCount', associations.same_device_count || 0);
        updateElement('bothSharedCount', associations.both_shared_count || 0);
        
        // 更新用户列表
        const updateUserList = (containerId, users) => {
            const container = document.getElementById(containerId);
            if (!container) {
                console.error(`❌ 容器 ${containerId} 不存在`);
                return false;
            }
            
            if (!users || users.length === 0) {
                container.innerHTML = '<div class="no-data">暂无关联用户</div>';
                console.log(`✅ ${containerId}: 显示无数据`);
                return true;
            }
            
            const html = users.map(user => `
                <div class="associated-user-item">
                    <div class="user-info">
                        <div class="user-header">
                            <span class="user-id">${user.member_id || user.digital_id}</span>
                            ${user.bd_name ? `<span class="bd-name">[${user.bd_name}]</span>` : ''}
                        </div>
                        <div class="shared-details">
                            ${user.shared_ip ? `<div class="shared-info ip-info">
                                <i class="bi bi-globe"></i> IP: <code>${user.shared_ip}</code>
                            </div>` : ''}
                            ${user.shared_device ? `<div class="shared-info device-info">
                                <i class="bi bi-phone"></i> 设备: <code>${user.shared_device.substring(0, 16)}...</code>
                            </div>` : ''}
                        </div>
                    </div>
                    <div class="user-actions">
                        <button class="btn btn-sm btn-outline-primary" onclick="searchRelatedUser('${user.member_id || user.digital_id}')" title="查看该用户详情">
                            <i class="bi bi-search"></i> 查看
                        </button>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = html;
            console.log(`✅ ${containerId}: 显示 ${users.length} 个用户`);
            return true;
        };
        
        updateUserList('sameIpUsers', associations.same_ip_users || []);
        updateUserList('sameDeviceUsers', associations.same_device_users || []);
        updateUserList('bothSharedUsers', associations.both_shared_users || []);
        
        console.log('✅ 手动更新显示完成');
        return true;
        
    } catch (error) {
        console.error('❌ 手动更新显示失败:', error);
        return false;
    }
}

// 4. 修复显示函数
function fixDisplayFunction() {
    console.log('=== 修复显示函数 ===');
    
    // 检查是否有userAnalysisApp
    if (window.userAnalysisApp && window.userAnalysisApp.displayAssociationsComplete) {
        console.log('✅ 找到 displayAssociationsComplete 函数');
        
        // 备份原函数
        window.originalDisplayAssociationsComplete = window.userAnalysisApp.displayAssociationsComplete;
        
        // 创建修复版本
        window.userAnalysisApp.displayAssociationsComplete = function(associations) {
            console.log('🔧 修复版 displayAssociationsComplete 被调用:', associations);
            
            if (!associations) {
                console.warn('⚠️ associations 参数为空');
                return;
            }
            
            // 调用原函数
            try {
                window.originalDisplayAssociationsComplete.call(this, associations);
                console.log('✅ 原函数调用成功');
            } catch (error) {
                console.error('❌ 原函数调用失败:', error);
            }
            
            // 额外的修复逻辑
            manualUpdateDisplay({ associations: associations });
        };
        
        console.log('✅ displayAssociationsComplete 函数已修复');
        return true;
    } else {
        console.error('❌ 未找到 displayAssociationsComplete 函数');
        return false;
    }
}

// 5. 完整的修复流程
async function fullFix() {
    console.log('🚀 开始完整修复流程...');
    
    // 检查当前状态
    checkCurrentState();
    
    // 修复显示函数
    fixDisplayFunction();
    
    // 手动获取数据并显示
    const data = await manualAPICall();
    if (data) {
        const success = manualUpdateDisplay(data);
        if (success) {
            console.log('🎉 修复完成！关联分析数据应该已经显示');
        } else {
            console.error('❌ 修复失败');
        }
    } else {
        console.error('❌ 无法获取API数据');
    }
}

// 6. 提供测试函数
window.testAssociationDisplay = function(testData) {
    const defaultTestData = {
        associations: {
            same_ip_count: 2,
            same_device_count: 1,
            both_shared_count: 1,
            same_ip_users: [
                {
                    member_id: '2198b7c8d4384fe9820a5dd5d5721c4b',
                    digital_id: '49428394',
                    shared_ip: '**************',
                    bd_name: 'Ourbit Official'
                }
            ],
            same_device_users: [
                {
                    member_id: '2198b7c8d4384fe9820a5dd5d5721c4b',
                    digital_id: '49428394',
                    shared_device: '86bec2285690ee9c1234567890abcdef',
                    bd_name: 'Ourbit Official'
                }
            ],
            both_shared_users: [
                {
                    member_id: '2198b7c8d4384fe9820a5dd5d5721c4b',
                    digital_id: '49428394',
                    shared_ip: '**************',
                    shared_device: '86bec2285690ee9c1234567890abcdef',
                    bd_name: 'Ourbit Official'
                }
            ]
        }
    };
    
    const dataToUse = testData || defaultTestData;
    console.log('🧪 测试关联分析显示:', dataToUse);
    manualUpdateDisplay(dataToUse);
};

// 执行完整修复
fullFix();

console.log('🔧 修复代码加载完成！');
console.log('💡 如果还有问题，可以手动调用:');
console.log('   - testAssociationDisplay() // 测试显示');
console.log('   - manualAPICall() // 手动API调用');
console.log('   - checkCurrentState() // 检查状态');
