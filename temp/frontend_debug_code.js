
// 前端调试代码 - 在浏览器控制台中运行

// 1. 检查当前页面的数据接收
function debugCurrentPageData() {
    console.log('=== 前端数据调试 ===');
    
    // 检查全局变量
    if (typeof window.userAnalysisManager !== 'undefined') {
        console.log('✅ userAnalysisManager 存在');
    } else {
        console.log('❌ userAnalysisManager 不存在');
    }
    
    // 检查最近的API响应
    if (window.lastApiResponse) {
        console.log('✅ 最近的API响应:', window.lastApiResponse);
        
        const associations = window.lastApiResponse.associations;
        if (associations) {
            console.log('关联分析数据:');
            console.log('- same_ip_count:', associations.same_ip_count);
            console.log('- same_device_count:', associations.same_device_count);
            console.log('- both_shared_count:', associations.both_shared_count);
            console.log('- same_ip_users length:', associations.same_ip_users?.length || 0);
            console.log('- same_device_users length:', associations.same_device_users?.length || 0);
            console.log('- both_shared_users length:', associations.both_shared_users?.length || 0);
        } else {
            console.log('❌ 没有关联分析数据');
        }
    } else {
        console.log('❌ 没有最近的API响应数据');
    }
}

// 2. 手动调用显示函数
function testDisplayAssociations() {
    console.log('=== 测试显示函数 ===');
    
    // 模拟测试数据
    const testData = {
        same_ip_count: 2,
        same_device_count: 1,
        both_shared_count: 1,
        same_ip_users: [
            {
                member_id: '2198b7c8d4384fe9820a5dd5d5721c4b',
                digital_id: '49428394',
                shared_ip: '**************',
                bd_name: 'Ourbit Official'
            }
        ],
        same_device_users: [
            {
                member_id: '2198b7c8d4384fe9820a5dd5d5721c4b',
                digital_id: '49428394',
                shared_device: '86bec2285690ee9c...',
                bd_name: 'Ourbit Official'
            }
        ],
        both_shared_users: [
            {
                member_id: '2198b7c8d4384fe9820a5dd5d5721c4b',
                digital_id: '49428394',
                shared_ip: '**************',
                shared_device: '86bec2285690ee9c...',
                bd_name: 'Ourbit Official'
            }
        ]
    };
    
    // 检查显示函数是否存在
    if (typeof window.userAnalysisManager !== 'undefined' && 
        typeof window.userAnalysisManager.displayAssociationsComplete === 'function') {
        console.log('✅ displayAssociationsComplete 函数存在');
        
        // 手动调用显示函数
        try {
            window.userAnalysisManager.displayAssociationsComplete(testData);
            console.log('✅ 手动调用显示函数成功');
        } catch (error) {
            console.error('❌ 手动调用显示函数失败:', error);
        }
    } else {
        console.log('❌ displayAssociationsComplete 函数不存在');
    }
}

// 3. 检查DOM元素
function checkDOMElements() {
    console.log('=== 检查DOM元素 ===');
    
    const elements = [
        'sameIpCount',
        'sameDeviceCount', 
        'bothSharedCount',
        'sameIpUsers',
        'sameDeviceUsers',
        'bothSharedUsers'
    ];
    
    elements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            console.log(`✅ ${id}: 存在, 内容: "${element.textContent || element.innerHTML}"`);
        } else {
            console.log(`❌ ${id}: 不存在`);
        }
    });
}

// 4. 监听网络请求
function monitorNetworkRequests() {
    console.log('=== 监听网络请求 ===');
    
    // 重写fetch函数来监听请求
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        console.log('🌐 Fetch请求:', args[0]);
        return originalFetch.apply(this, args).then(response => {
            if (args[0].includes('user-analysis')) {
                console.log('📥 用户分析API响应:', response.status);
                response.clone().json().then(data => {
                    console.log('📊 响应数据:', data);
                    window.lastApiResponse = data;
                }).catch(e => console.log('解析响应数据失败:', e));
            }
            return response;
        });
    };
    
    console.log('✅ 网络请求监听已启用');
}

// 运行所有调试函数
console.log('🔍 开始前端调试...');
debugCurrentPageData();
checkDOMElements();
testDisplayAssociations();
monitorNetworkRequests();
console.log('✅ 前端调试完成');
