
// 前端验证脚本 - 在浏览器控制台中运行

console.log('🔍 开始验证修复效果...');

// 1. 检查修复是否生效
function checkFix() {
    console.log('=== 检查修复状态 ===');
    
    // 检查SearchManager是否存在
    const searchManager = window.userAnalysisApp?.searchManager;
    if (searchManager) {
        console.log('✅ SearchManager 存在');
        
        // 检查是否有保存的关联数据
        if (searchManager._savedAssociations) {
            console.log('✅ 发现保存的关联数据:', searchManager._savedAssociations);
        } else {
            console.log('ℹ️ 当前没有保存的关联数据（正常，搜索后才会有）');
        }
    } else {
        console.log('❌ SearchManager 不存在');
    }
    
    // 检查当前用户数据
    const currentData = window.userAnalysisApp?.state?.currentUserData;
    if (currentData && currentData.complete_data) {
        console.log('✅ 当前用户数据存在');
        
        const associations = currentData.complete_data.associations;
        if (associations) {
            console.log('✅ 关联分析数据存在:', associations);
            console.log('  - 同IP用户:', associations.same_ip_count || 0);
            console.log('  - 同设备用户:', associations.same_device_count || 0);
            console.log('  - 同时共享用户:', associations.both_shared_count || 0);
        } else {
            console.log('❌ 关联分析数据不存在');
        }
    } else {
        console.log('ℹ️ 当前没有用户数据（需要先搜索）');
    }
}

// 2. 模拟搜索流程
async function simulateSearch() {
    console.log('=== 模拟搜索流程 ===');
    
    const testDigitalId = '23452801';
    console.log(`模拟搜索用户: ${testDigitalId}`);
    
    // 检查输入框
    const userIdInput = document.getElementById('userIdInput');
    if (userIdInput) {
        userIdInput.value = testDigitalId;
        console.log('✅ 已填入测试用户ID');
        
        // 触发输入事件
        userIdInput.dispatchEvent(new Event('input'));
        
        // 模拟点击搜索按钮
        const searchBtn = document.getElementById('searchBtn');
        if (searchBtn) {
            console.log('🔍 模拟点击搜索按钮...');
            searchBtn.click();
        } else {
            console.log('❌ 搜索按钮不存在');
        }
    } else {
        console.log('❌ 用户ID输入框不存在');
    }
}

// 3. 检查DOM显示
function checkDOMDisplay() {
    console.log('=== 检查DOM显示 ===');
    
    const elements = [
        { id: 'sameIpCount', name: '同IP用户数' },
        { id: 'sameDeviceCount', name: '同设备用户数' },
        { id: 'bothSharedCount', name: '同时共享用户数' },
        { id: 'sameIpUsers', name: '同IP用户列表' },
        { id: 'sameDeviceUsers', name: '同设备用户列表' },
        { id: 'bothSharedUsers', name: '同时共享用户列表' }
    ];
    
    elements.forEach(({ id, name }) => {
        const element = document.getElementById(id);
        if (element) {
            const content = element.textContent || element.innerHTML;
            console.log(`✅ ${name}: "${content}"`);
        } else {
            console.log(`❌ ${name}: 元素不存在`);
        }
    });
}

// 4. 完整验证流程
async function fullVerification() {
    console.log('🚀 开始完整验证流程...');
    
    // 检查修复状态
    checkFix();
    
    // 检查当前DOM显示
    checkDOMDisplay();
    
    // 如果没有数据，模拟搜索
    const currentData = window.userAnalysisApp?.state?.currentUserData;
    if (!currentData) {
        console.log('📝 没有当前数据，开始模拟搜索...');
        await simulateSearch();
        
        // 等待搜索完成
        setTimeout(() => {
            console.log('🔍 搜索完成，重新检查...');
            checkFix();
            checkDOMDisplay();
        }, 3000);
    }
}

// 运行验证
fullVerification();

console.log('✅ 验证脚本加载完成！');
console.log('💡 可以手动调用以下函数:');
console.log('  - checkFix() // 检查修复状态');
console.log('  - checkDOMDisplay() // 检查DOM显示');
console.log('  - simulateSearch() // 模拟搜索');
