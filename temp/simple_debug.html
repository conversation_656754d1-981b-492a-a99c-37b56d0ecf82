<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单调试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .button { background: #007bff; color: white; border: none; padding: 10px 15px; margin: 5px; border-radius: 3px; cursor: pointer; }
        .button:hover { background: #0056b3; }
        .result { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 3px; font-family: monospace; white-space: pre-wrap; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>异常交易显示问题调试</h1>
    
    <div class="debug-section">
        <h3>1. 测试API调用</h3>
        <button class="button" onclick="testAPI()">测试API</button>
        <div id="apiResult" class="result"></div>
    </div>
    
    <div class="debug-section">
        <h3>2. 模拟前端处理</h3>
        <button class="button" onclick="simulateFrontend()">模拟前端处理</button>
        <div id="frontendResult" class="result"></div>
    </div>
    
    <div class="debug-section">
        <h3>3. 检查DOM元素</h3>
        <button class="button" onclick="checkDOM()">检查DOM</button>
        <div id="domResult" class="result"></div>
    </div>
    
    <div class="debug-section">
        <h3>4. 模拟交易列表显示</h3>
        <div id="mockTransactionsList" style="border: 1px solid #ccc; padding: 10px; min-height: 100px; background: white;">
            等待数据...
        </div>
    </div>

    <script>
        let apiData = null;
        
        async function testAPI() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.textContent = '正在测试API...';
            
            try {
                // 先登录
                const loginResponse = await fetch('http://localhost:5005/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'admin', password: 'admin123' }),
                    credentials: 'include'
                });
                
                if (!loginResponse.ok) {
                    throw new Error('登录失败');
                }
                
                // 调用用户分析API
                const response = await fetch('http://localhost:5005/api/user-analysis/search/9c8db566f6354095bf237e962d0ab4c5', {
                    credentials: 'include'
                });
                
                if (!response.ok) {
                    throw new Error(`API调用失败: ${response.status}`);
                }
                
                apiData = await response.json();
                
                let result = '✅ API调用成功\n';
                result += `用户ID: ${apiData.member_id}\n`;
                result += `状态: ${apiData.status}\n`;
                
                if (apiData.transaction_details) {
                    result += `transaction_details存在: ✅\n`;
                    result += `结构: ${Object.keys(apiData.transaction_details).join(', ')}\n`;
                    
                    if (apiData.transaction_details.risk_transactions) {
                        result += `risk_transactions数量: ${apiData.transaction_details.risk_transactions.length}\n`;
                        result += `前3条数据:\n`;
                        apiData.transaction_details.risk_transactions.slice(0, 3).forEach((tx, i) => {
                            result += `  ${i+1}. ${tx.detection_type}, 风险评分=${tx.risk_score}, 交易量=${tx.volume || tx.abnormal_volume}\n`;
                        });
                    } else {
                        result += `❌ 没有risk_transactions字段\n`;
                    }
                } else {
                    result += `❌ 没有transaction_details\n`;
                }
                
                resultDiv.textContent = result;
                resultDiv.className = 'result success';
                
            } catch (error) {
                resultDiv.textContent = `❌ 错误: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }
        
        function simulateFrontend() {
            const resultDiv = document.getElementById('frontendResult');
            
            if (!apiData) {
                resultDiv.textContent = '❌ 请先运行API测试';
                resultDiv.className = 'result error';
                return;
            }
            
            let result = '🔍 模拟前端处理逻辑\n';
            
            // 模拟main-refactored.js的逻辑
            if (apiData.transaction_details) {
                result += '✅ 找到transaction_details\n';
                
                const riskTransactions = apiData.transaction_details.risk_transactions || [];
                result += `✅ 提取risk_transactions: ${riskTransactions.length}条\n`;
                
                if (riskTransactions.length > 0) {
                    result += '✅ 应该调用transactionDetailsManager.display()\n';
                    
                    // 模拟显示逻辑
                    displayMockTransactions(riskTransactions);
                    result += '✅ 已在下方显示模拟交易列表\n';
                } else {
                    result += '❌ 应该调用showNoDataMessage()\n';
                    displayNoData();
                }
            } else {
                result += '❌ 没有transaction_details\n';
            }
            
            resultDiv.textContent = result;
            resultDiv.className = 'result success';
        }
        
        function checkDOM() {
            const resultDiv = document.getElementById('domResult');
            
            // 检查关键DOM元素（在真实页面中）
            const elements = [
                'transactionsList',
                'userIdInput', 
                'searchBtn'
            ];
            
            let result = '🔍 检查DOM元素\n';
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    result += `✅ ${id}: 存在\n`;
                } else {
                    result += `❌ ${id}: 不存在\n`;
                }
            });
            
            // 检查是否在iframe中
            if (window.parent !== window) {
                result += '⚠️ 当前在iframe中，可能无法访问主页面元素\n';
            }
            
            resultDiv.textContent = result;
            resultDiv.className = 'result warning';
        }
        
        function displayMockTransactions(transactions) {
            const container = document.getElementById('mockTransactionsList');
            
            if (!transactions || transactions.length === 0) {
                container.innerHTML = '<div style="color: #666;">暂无风险交易记录</div>';
                return;
            }
            
            const html = transactions.slice(0, 10).map((tx, index) => {
                return `
                    <div style="border-bottom: 1px solid #eee; padding: 8px 0;">
                        <strong>交易${index + 1}:</strong> 
                        ${formatRiskType(tx.detection_type)} | 
                        风险评分: ${tx.risk_score || 0} | 
                        交易量: ${formatNumber(tx.volume || tx.abnormal_volume || 0)} USDT | 
                        时间: ${formatTime(tx.timestamp || tx.created_at)}
                    </div>
                `;
            }).join('');
            
            container.innerHTML = html;
        }
        
        function displayNoData() {
            const container = document.getElementById('mockTransactionsList');
            container.innerHTML = '<div style="color: #666; text-align: center; padding: 20px;">暂无风险交易记录</div>';
        }
        
        function formatRiskType(type) {
            const typeMap = {
                'wash_trading': '对敲交易',
                'high_frequency': '高频交易',
                'arbitrage': '套利交易'
            };
            return typeMap[type] || type || '未知';
        }
        
        function formatNumber(num) {
            if (!num) return '0';
            return parseFloat(num).toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }
        
        function formatTime(timeStr) {
            if (!timeStr) return 'N/A';
            try {
                const date = new Date(timeStr);
                return date.toLocaleString('zh-CN');
            } catch (error) {
                return timeStr;
            }
        }
    </script>
</body>
</html>
