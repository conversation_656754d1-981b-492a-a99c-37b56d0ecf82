#!/usr/bin/env python3
"""
测试修复后的前端关联分析功能
"""
import time

def test_fixed_frontend():
    """测试修复后的前端功能"""
    print("🎯 测试修复后的前端关联分析功能")
    print("=" * 60)
    
    print("✅ 修复内容:")
    print("1. 在 SearchManager 中保存 search-by-digital API 返回的关联分析数据")
    print("2. 将关联分析数据合并到 complete-analysis API 的结果中")
    print("3. 确保 displayAssociationsComplete() 接收到正确的数据")
    
    print(f"\n📝 测试步骤:")
    print("1. 刷新浏览器页面以加载修复后的代码")
    print("2. 打开个人分析页面:")
    print("   http://localhost:3000/src/modules/user-analysis/pages/user-analysis.html")
    print("3. 输入测试用户ID: 23452801")
    print("4. 选择代理分析任务")
    print("5. 点击'智能搜索'")
    print("6. 查看关联分析部分是否显示正确数据")
    
    print(f"\n🔍 预期结果:")
    print("- 同IP用户: 2 个")
    print("- 同设备用户: 1 个")
    print("- 同时共享用户: 1 个")
    print("- 关联用户详情: 2198b7c8d4384fe9820a5dd5d5721c4b")
    print("- 共享IP: ************** 和 2607:fb91:608:5222:29aa:f66f:1060:66c8")
    print("- 共享设备: 86bec2285690ee9c...")
    print("- BD团队: Ourbit Official")
    
    print(f"\n🛠️ 调试方法:")
    print("如果还有问题，在浏览器控制台中运行:")
    print("```javascript")
    print("// 检查数据合并")
    print("console.log('SearchManager:', window.userAnalysisApp?.searchManager);")
    print("console.log('保存的关联数据:', window.userAnalysisApp?.searchManager?._savedAssociations);")
    print("")
    print("// 检查最终数据")
    print("console.log('当前用户数据:', window.userAnalysisApp?.state?.currentUserData);")
    print("console.log('关联分析数据:', window.userAnalysisApp?.state?.currentUserData?.complete_data?.associations);")
    print("```")
    
    print(f"\n📊 技术细节:")
    print("修复原理:")
    print("1. search-by-digital API 返回正确的关联分析数据")
    print("2. SearchManager 将这些数据临时保存在 _savedAssociations 中")
    print("3. 当 complete-analysis API 返回数据后，将保存的关联数据合并进去")
    print("4. displayCompleteUserAnalysis() 接收到包含关联数据的完整结构")
    print("5. displayAssociationsComplete() 正常显示关联用户")
    
    print(f"\n🎉 修复完成！")
    print("现在前端应该能正确显示关联分析数据了。")

def generate_verification_script():
    """生成验证脚本"""
    print(f"\n📋 生成前端验证脚本")
    print("=" * 50)
    
    verification_script = """
// 前端验证脚本 - 在浏览器控制台中运行

console.log('🔍 开始验证修复效果...');

// 1. 检查修复是否生效
function checkFix() {
    console.log('=== 检查修复状态 ===');
    
    // 检查SearchManager是否存在
    const searchManager = window.userAnalysisApp?.searchManager;
    if (searchManager) {
        console.log('✅ SearchManager 存在');
        
        // 检查是否有保存的关联数据
        if (searchManager._savedAssociations) {
            console.log('✅ 发现保存的关联数据:', searchManager._savedAssociations);
        } else {
            console.log('ℹ️ 当前没有保存的关联数据（正常，搜索后才会有）');
        }
    } else {
        console.log('❌ SearchManager 不存在');
    }
    
    // 检查当前用户数据
    const currentData = window.userAnalysisApp?.state?.currentUserData;
    if (currentData && currentData.complete_data) {
        console.log('✅ 当前用户数据存在');
        
        const associations = currentData.complete_data.associations;
        if (associations) {
            console.log('✅ 关联分析数据存在:', associations);
            console.log('  - 同IP用户:', associations.same_ip_count || 0);
            console.log('  - 同设备用户:', associations.same_device_count || 0);
            console.log('  - 同时共享用户:', associations.both_shared_count || 0);
        } else {
            console.log('❌ 关联分析数据不存在');
        }
    } else {
        console.log('ℹ️ 当前没有用户数据（需要先搜索）');
    }
}

// 2. 模拟搜索流程
async function simulateSearch() {
    console.log('=== 模拟搜索流程 ===');
    
    const testDigitalId = '23452801';
    console.log(`模拟搜索用户: ${testDigitalId}`);
    
    // 检查输入框
    const userIdInput = document.getElementById('userIdInput');
    if (userIdInput) {
        userIdInput.value = testDigitalId;
        console.log('✅ 已填入测试用户ID');
        
        // 触发输入事件
        userIdInput.dispatchEvent(new Event('input'));
        
        // 模拟点击搜索按钮
        const searchBtn = document.getElementById('searchBtn');
        if (searchBtn) {
            console.log('🔍 模拟点击搜索按钮...');
            searchBtn.click();
        } else {
            console.log('❌ 搜索按钮不存在');
        }
    } else {
        console.log('❌ 用户ID输入框不存在');
    }
}

// 3. 检查DOM显示
function checkDOMDisplay() {
    console.log('=== 检查DOM显示 ===');
    
    const elements = [
        { id: 'sameIpCount', name: '同IP用户数' },
        { id: 'sameDeviceCount', name: '同设备用户数' },
        { id: 'bothSharedCount', name: '同时共享用户数' },
        { id: 'sameIpUsers', name: '同IP用户列表' },
        { id: 'sameDeviceUsers', name: '同设备用户列表' },
        { id: 'bothSharedUsers', name: '同时共享用户列表' }
    ];
    
    elements.forEach(({ id, name }) => {
        const element = document.getElementById(id);
        if (element) {
            const content = element.textContent || element.innerHTML;
            console.log(`✅ ${name}: "${content}"`);
        } else {
            console.log(`❌ ${name}: 元素不存在`);
        }
    });
}

// 4. 完整验证流程
async function fullVerification() {
    console.log('🚀 开始完整验证流程...');
    
    // 检查修复状态
    checkFix();
    
    // 检查当前DOM显示
    checkDOMDisplay();
    
    // 如果没有数据，模拟搜索
    const currentData = window.userAnalysisApp?.state?.currentUserData;
    if (!currentData) {
        console.log('📝 没有当前数据，开始模拟搜索...');
        await simulateSearch();
        
        // 等待搜索完成
        setTimeout(() => {
            console.log('🔍 搜索完成，重新检查...');
            checkFix();
            checkDOMDisplay();
        }, 3000);
    }
}

// 运行验证
fullVerification();

console.log('✅ 验证脚本加载完成！');
console.log('💡 可以手动调用以下函数:');
console.log('  - checkFix() // 检查修复状态');
console.log('  - checkDOMDisplay() // 检查DOM显示');
console.log('  - simulateSearch() // 模拟搜索');
"""
    
    with open('frontend_verification.js', 'w', encoding='utf-8') as f:
        f.write(verification_script)
    
    print("   ✅ 前端验证脚本已保存到 frontend_verification.js")

if __name__ == "__main__":
    test_fixed_frontend()
    generate_verification_script()
    
    print(f"\n🎯 立即测试:")
    print("1. 刷新浏览器页面")
    print("2. 输入测试用户ID: 23452801")
    print("3. 点击搜索")
    print("4. 查看关联分析部分")
    print("5. 如有问题，运行验证脚本调试")
