/**
 * 测试前端控制台输出的脚本
 * 在浏览器控制台中运行此脚本来调试异常交易显示问题
 */

// 测试函数：模拟搜索用户
async function testUserSearch() {
    console.log('🚀 开始测试用户搜索功能');
    
    // 检查页面是否已加载
    if (!window.userAnalysisApp) {
        console.error('❌ userAnalysisApp 未初始化');
        return;
    }
    
    console.log('✅ userAnalysisApp 已初始化');
    
    // 模拟搜索用户
    const testUserId = '9c8db566f6354095bf237e962d0ab4c5';
    console.log(`🔍 搜索用户: ${testUserId}`);
    
    try {
        // 直接调用搜索方法
        await window.userAnalysisApp.searchManager.searchUser(testUserId);
        console.log('✅ 搜索完成');
    } catch (error) {
        console.error('❌ 搜索失败:', error);
    }
}

// 测试函数：检查DOM元素
function checkDOMElements() {
    console.log('🔍 检查关键DOM元素');
    
    const elements = {
        transactionsList: document.getElementById('transactionsList'),
        searchInput: document.getElementById('userSearchInput'),
        searchButton: document.querySelector('.search-button')
    };
    
    Object.entries(elements).forEach(([name, element]) => {
        if (element) {
            console.log(`✅ ${name}: 存在`);
        } else {
            console.error(`❌ ${name}: 不存在`);
        }
    });
    
    return elements;
}

// 测试函数：检查TransactionDetailsManager
function checkTransactionDetailsManager() {
    console.log('🔍 检查TransactionDetailsManager');
    
    if (window.transactionDetailsManager) {
        console.log('✅ transactionDetailsManager 存在');
        console.log('🔍 allTransactions:', window.transactionDetailsManager.allTransactions);
        console.log('🔍 filteredTransactions:', window.transactionDetailsManager.filteredTransactions);
    } else {
        console.error('❌ transactionDetailsManager 不存在');
    }
}

// 测试函数：直接调用API
async function testDirectAPI() {
    console.log('🚀 直接测试API调用');
    
    try {
        const response = await fetch('/api/user-analysis/search/9c8db566f6354095bf237e962d0ab4c5', {
            credentials: 'include'
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log('✅ API响应成功');
        console.log('🔍 transaction_details:', data.transaction_details);
        
        if (data.transaction_details && data.transaction_details.risk_transactions) {
            console.log(`✅ 找到 ${data.transaction_details.risk_transactions.length} 条风险交易`);
            
            // 手动调用显示函数
            if (window.transactionDetailsManager) {
                console.log('🔧 手动调用transactionDetailsManager.display()');
                window.transactionDetailsManager.display(data.transaction_details.risk_transactions);
            }
        } else {
            console.warn('❌ 没有找到risk_transactions数据');
        }
        
        return data;
    } catch (error) {
        console.error('❌ API调用失败:', error);
        return null;
    }
}

// 测试函数：检查筛选器
function checkFilters() {
    console.log('🔍 检查筛选器状态');
    
    if (window.transactionDetailsManager) {
        const manager = window.transactionDetailsManager;
        console.log('🔍 当前筛选器:', manager.currentFilters);
        console.log('🔍 排序字段:', manager.sortField);
        console.log('🔍 排序方向:', manager.sortDirection);
        
        // 重置筛选器
        console.log('🔧 重置筛选器');
        manager.currentFilters = {};
        manager.sortField = 'time';
        manager.sortDirection = 'desc';
        
        if (manager.allTransactions && manager.allTransactions.length > 0) {
            console.log('🔧 重新应用筛选和排序');
            manager.applyFiltersAndSort();
        }
    }
}

// 主测试函数
async function runAllTests() {
    console.log('🚀 开始完整测试流程');
    console.log('='.repeat(50));
    
    // 1. 检查DOM元素
    checkDOMElements();
    console.log('-'.repeat(30));
    
    // 2. 检查TransactionDetailsManager
    checkTransactionDetailsManager();
    console.log('-'.repeat(30));
    
    // 3. 直接测试API
    const apiData = await testDirectAPI();
    console.log('-'.repeat(30));
    
    // 4. 检查筛选器
    checkFilters();
    console.log('-'.repeat(30));
    
    // 5. 测试用户搜索
    await testUserSearch();
    
    console.log('='.repeat(50));
    console.log('✅ 测试完成');
}

// 导出测试函数到全局作用域
window.testUserSearch = testUserSearch;
window.checkDOMElements = checkDOMElements;
window.checkTransactionDetailsManager = checkTransactionDetailsManager;
window.testDirectAPI = testDirectAPI;
window.checkFilters = checkFilters;
window.runAllTests = runAllTests;

console.log('🔧 测试脚本已加载，可用函数:');
console.log('- runAllTests(): 运行所有测试');
console.log('- testUserSearch(): 测试用户搜索');
console.log('- checkDOMElements(): 检查DOM元素');
console.log('- checkTransactionDetailsManager(): 检查交易管理器');
console.log('- testDirectAPI(): 直接测试API');
console.log('- checkFilters(): 检查筛选器');
console.log('');
console.log('💡 建议先运行: runAllTests()');
