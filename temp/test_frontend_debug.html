<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端数据处理调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .log-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>前端异常交易数据处理调试</h1>
        
        <div class="controls">
            <button class="test-button" onclick="runTest()">运行测试</button>
            <button class="test-button" onclick="clearLog()">清空日志</button>
            <button class="test-button" onclick="testRealAPI()">测试真实API</button>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <h3>测试日志:</h3>
        <div id="logOutput" class="log-section">点击"运行测试"开始调试...</div>
        
        <h3>模拟交易列表显示:</h3>
        <div id="mockTransactionsList" class="log-section">等待数据...</div>
    </div>

    <script>
        let logOutput = '';
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'warning' ? '⚠️' : type === 'success' ? '✅' : 'ℹ️';
            logOutput += `[${timestamp}] ${prefix} ${message}\n`;
            document.getElementById('logOutput').textContent = logOutput;
            document.getElementById('logOutput').scrollTop = document.getElementById('logOutput').scrollHeight;
        }
        
        function clearLog() {
            logOutput = '';
            document.getElementById('logOutput').textContent = '日志已清空...';
            document.getElementById('mockTransactionsList').textContent = '等待数据...';
            document.getElementById('status').style.display = 'none';
        }
        
        function showStatus(message, type) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
            statusEl.style.display = 'block';
        }
        
        async function runTest() {
            log('🚀 开始前端数据处理调试测试', 'info');
            clearLog();
            
            try {
                // 加载API响应数据
                log('正在加载API响应数据...', 'info');
                const response = await fetch('api_response_debug.json');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const userData = await response.json();
                log('✅ 成功加载API响应数据', 'success');
                log(`用户ID: ${userData.member_id}`, 'info');
                log(`响应状态: ${userData.status}`, 'info');
                
                // 处理交易详情
                processTransactionDetails(userData);
                
                showStatus('测试完成！检查日志了解详细信息。', 'success');
                
            } catch (error) {
                log(`❌ 测试失败: ${error.message}`, 'error');
                showStatus(`测试失败: ${error.message}`, 'error');
            }
        }
        
        function processTransactionDetails(userData) {
            log('=== 开始处理交易详情 ===', 'info');
            
            if (!userData.transaction_details) {
                log('❌ 没有transaction_details数据', 'error');
                return;
            }
            
            log('✅ 找到transaction_details', 'success');
            log(`transaction_details结构: ${Object.keys(userData.transaction_details).join(', ')}`, 'info');
            
            // 检查risk_transactions字段
            const riskTransactions = userData.transaction_details.risk_transactions || [];
            log(`✅ risk_transactions数量: ${riskTransactions.length}`, 'success');
            
            if (riskTransactions.length > 0) {
                log('前3条风险交易:', 'info');
                riskTransactions.slice(0, 3).forEach((tx, index) => {
                    log(`  交易${index + 1}: ${tx.detection_type}, 风险评分=${tx.risk_score}, 交易量=${tx.volume || tx.abnormal_volume}`, 'info');
                });
                
                // 模拟前端显示逻辑
                displayRiskTransactionsList(riskTransactions);
            } else {
                log('❌ 没有风险交易数据', 'warning');
                showNoDataMessage();
            }
        }
        
        function displayRiskTransactionsList(transactions) {
            log('\n=== 模拟前端显示逻辑 ===', 'info');
            
            if (!transactions.length) {
                log('显示: 暂无风险交易记录', 'warning');
                document.getElementById('mockTransactionsList').textContent = '暂无风险交易记录';
                return;
            }
            
            log(`准备显示 ${transactions.length} 条交易记录`, 'success');
            
            // 生成模拟HTML
            const mockHTML = transactions.slice(0, 10).map((tx, index) => {
                return `交易${index + 1}: ${formatRiskType(tx.detection_type)} | 风险评分: ${tx.risk_score || 0} | 交易量: ${formatNumber(tx.volume || tx.abnormal_volume || 0)} USDT | 时间: ${formatTime(tx.timestamp || tx.created_at)}`;
            }).join('\n');
            
            document.getElementById('mockTransactionsList').textContent = mockHTML;
            log('✅ 成功生成交易列表显示', 'success');
        }
        
        function showNoDataMessage() {
            log('显示: 暂无风险交易记录', 'warning');
            document.getElementById('mockTransactionsList').textContent = '暂无风险交易记录';
        }
        
        // 辅助函数
        function formatRiskType(type) {
            const typeMap = {
                'wash_trading': '对敲交易',
                'high_frequency': '高频交易',
                'arbitrage': '套利交易'
            };
            return typeMap[type] || type || '未知';
        }
        
        function formatNumber(num) {
            if (!num) return '0';
            return parseFloat(num).toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }
        
        function formatTime(timeStr) {
            if (!timeStr) return 'N/A';
            try {
                const date = new Date(timeStr);
                return date.toLocaleString('zh-CN');
            } catch (error) {
                return timeStr;
            }
        }
        
        async function testRealAPI() {
            log('🚀 测试真实API调用', 'info');
            
            try {
                // 先登录
                const loginResponse = await fetch('http://localhost:5005/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    }),
                    credentials: 'include'
                });
                
                if (!loginResponse.ok) {
                    throw new Error('登录失败');
                }
                
                log('✅ 登录成功', 'success');
                
                // 调用用户分析API
                const apiResponse = await fetch('http://localhost:5005/api/user-analysis/search/9c8db566f6354095bf237e962d0ab4c5', {
                    credentials: 'include'
                });
                
                if (!apiResponse.ok) {
                    throw new Error(`API调用失败: ${apiResponse.status}`);
                }
                
                const userData = await apiResponse.json();
                log('✅ API调用成功', 'success');
                
                // 处理数据
                processTransactionDetails(userData);
                
                showStatus('真实API测试完成！', 'success');
                
            } catch (error) {
                log(`❌ 真实API测试失败: ${error.message}`, 'error');
                showStatus(`真实API测试失败: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
